#!/bin/bash

echo "🔧 Stopping and removing Docker engine if installed..."
sudo systemctl stop docker 2>/dev/null
sudo systemctl disable docker 2>/dev/null
sudo apt remove -y docker docker-engine docker.io containerd runc
sudo apt autoremove -y

echo "🧹 Removing old Docker data..."
sudo rm -rf /var/lib/docker
sudo rm -rf /var/lib/containerd
sudo rm -f /usr/local/bin/docker
sudo rm -f /usr/bin/docker

echo "🧼 Cleaning up old containers, images, and volumes (if any)..."
docker ps -aq >/dev/null 2>&1 && docker rm -f $(docker ps -aq) 2>/dev/null
docker images -q >/dev/null 2>&1 && docker rmi -f $(docker images -q) 2>/dev/null
docker volume ls -q >/dev/null 2>&1 && docker volume rm $(docker volume ls -q) 2>/dev/null

echo "🛠 Installing Docker CLI only..."
sudo apt update
sudo apt install -y docker-cli

echo "🔗 Verifying Docker socket access..."
if [ ! -S /var/run/docker.sock ]; then
    echo "⚠️  Docker socket not found, setting DOCKER_HOST as fallback..."
    echo 'export DOCKER_HOST=unix:///mnt/wsl/docker-desktop/docker.sock' >> ~/.bashrc
    echo '✅ Added DOCKER_HOST to ~/.bashrc'
    export DOCKER_HOST=unix:///mnt/wsl/docker-desktop/docker.sock
else
    echo "✅ Docker socket found: /var/run/docker.sock"
fi

echo "✅ Docker should now use Docker Desktop backend. Testing..."

docker info | grep -i 'server version\|docker root dir'

echo "🎉 Done. You may need to restart your shell or WSL session."
