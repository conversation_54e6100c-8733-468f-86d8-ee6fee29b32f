-- Add n8n_execution_id column to executions table
ALTER TABLE public.executions
ADD COLUMN IF NOT EXISTS n8n_execution_id VARCHAR(255) DEFAULT NULL;

-- Add comment to the column
COMMENT ON COLUMN public.executions.n8n_execution_id IS 'Stores the n8n workflow execution ID for tracking and debugging';

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_executions_n8n_execution_id ON public.executions(n8n_execution_id);

-- Add started_at and completed_at columns if they don't exist
ALTER TABLE public.executions
ADD COLUMN IF NOT EXISTS started_at TIMESTAMPTZ DEFAULT NULL;

ALTER TABLE public.executions
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMPTZ DEFAULT NULL;

-- Add comments to the columns
COMMENT ON COLUMN public.executions.started_at IS 'Timestamp when the execution started running';
COMMENT ON COLUMN public.executions.completed_at IS 'Timestamp when the execution completed (successfully or with error)';
