-- Create Observation Notes Domain Tables
-- This migration creates the observation notes system with WYSIWYG editor support,
-- approval workflow, and comprehensive audit trail.

-- Note status enum
CREATE TYPE note_status AS ENUM ('draft', 'pending_approval', 'approved', 'rejected');

-- Main observation notes table
CREATE TABLE observation_notes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

  -- Universal attachment (polymorphic)
  attached_to_type TEXT NOT NULL, -- 'appointment', 'case_file'
  attached_to_id UUID NOT NULL, -- appointment_id or case_file_id

  -- Note content
  title TEXT NOT NULL,
  content TEXT NOT NULL, -- WYSIWYG HTML content

  -- Status and workflow
  status note_status NOT NULL DEFAULT 'draft',

  -- Authorship
  created_by UUID NOT NULL REFERENCES auth.users(id), -- Social worker
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Approval workflow
  approval_requested_at TIMESTAMP WITH TIME ZONE,
  approved_by UUID REFERENCES auth.users(id), -- Director/Coordinator
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,

  -- Metadata
  metadata JSONB DEFAULT '{}',

  -- Constraints
  CONSTRAINT fk_observation_notes_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_observation_notes_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT fk_observation_notes_approved_by FOREIGN KEY (approved_by) REFERENCES auth.users(id)
);

-- Note history table (tracks all changes)
CREATE TABLE observation_note_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  note_id UUID NOT NULL REFERENCES observation_notes(id) ON DELETE CASCADE,

  -- Change details
  action TEXT NOT NULL, -- 'created', 'updated', 'approval_requested', 'approved', 'rejected'
  field_changes JSONB, -- What fields changed (title, content, status)
  previous_content TEXT, -- Previous content for content changes
  new_content TEXT, -- New content for content changes

  -- Who made the change
  changed_by UUID NOT NULL REFERENCES auth.users(id),
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Change context
  change_reason TEXT, -- Why the change was made
  metadata JSONB DEFAULT '{}',

  -- Constraints
  CONSTRAINT fk_observation_note_history_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_observation_note_history_note FOREIGN KEY (note_id) REFERENCES observation_notes(id),
  CONSTRAINT fk_observation_note_history_changed_by FOREIGN KEY (changed_by) REFERENCES auth.users(id)
);

-- Create indexes for performance
CREATE INDEX idx_observation_notes_organization_id ON observation_notes(organization_id);
CREATE INDEX idx_observation_notes_attached_to ON observation_notes(attached_to_type, attached_to_id);
CREATE INDEX idx_observation_notes_status ON observation_notes(status);
CREATE INDEX idx_observation_notes_created_by ON observation_notes(created_by);
CREATE INDEX idx_observation_notes_approved_by ON observation_notes(approved_by);
CREATE INDEX idx_observation_notes_created_at ON observation_notes(created_at);

CREATE INDEX idx_observation_note_history_organization_id ON observation_note_history(organization_id);
CREATE INDEX idx_observation_note_history_note_id ON observation_note_history(note_id);
CREATE INDEX idx_observation_note_history_action ON observation_note_history(action);
CREATE INDEX idx_observation_note_history_changed_by ON observation_note_history(changed_by);
CREATE INDEX idx_observation_note_history_changed_at ON observation_note_history(changed_at);

-- Create triggers for updated_at
CREATE TRIGGER update_observation_notes_updated_at
BEFORE UPDATE ON observation_notes
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE observation_notes IS 'Observation notes with WYSIWYG content and approval workflow';
COMMENT ON TABLE observation_note_history IS 'Complete audit trail for all observation note changes and approvals';

COMMENT ON COLUMN observation_notes.attached_to_type IS 'Entity type: appointment or case_file';
COMMENT ON COLUMN observation_notes.content IS 'WYSIWYG HTML content from rich text editor';
COMMENT ON COLUMN observation_notes.status IS 'Approval workflow status: draft, pending_approval, approved, rejected';
COMMENT ON COLUMN observation_note_history.action IS 'Type of change: created, updated, approval_requested, approved, rejected';
COMMENT ON COLUMN observation_note_history.field_changes IS 'JSONB tracking which specific fields changed';