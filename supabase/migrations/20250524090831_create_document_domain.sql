-- Create Document Domain Tables
-- This migration creates the document management system with templates, universal attachments,
-- and electronic signature functionality.

-- Document templates table
CREATE TABLE document_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  template_type TEXT NOT NULL, -- 'service_agreement', 'consent_form', 'assessment_report'
  content TEXT NOT NULL, -- WYSIWYG HTML content with tokens like {{contact_name}}
  language TEXT NOT NULL DEFAULT 'fr', -- 'fr', 'en'
  service_id UUID REFERENCES services(id), -- Which service uses this template
  is_active BOOLEAN DEFAULT true,
  signature_required BOOLEAN DEFAULT false,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  metadata JSONB DEFAULT '{}', -- Template configuration, token definitions

  -- Constraints
  CONSTRAINT fk_document_templates_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_document_templates_service FOREIGN KEY (service_id) REFERENCES services(id),
  CONSTRAINT fk_document_templates_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id)
);

-- Universal document attachments table (both generated and manual)
CREATE TABLE document_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

  -- Universal attachment (polymorphic)
  attached_to_type TEXT NOT NULL, -- 'case_file', 'request', 'contact', 'appointment', 'observation'
  attached_to_id UUID NOT NULL, -- ID of the entity it's attached to

  -- Document info
  document_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- Storage path (always required)
  document_type TEXT NOT NULL, -- 'PDF', 'DOCX', 'JPG', 'PNG', etc.
  file_size INTEGER, -- File size in bytes

  -- Generation info (nullable for manual uploads)
  template_id UUID REFERENCES document_templates(id), -- NULL for manual uploads
  contact_id UUID REFERENCES contacts(id), -- NULL if not contact-specific
  language TEXT DEFAULT 'fr', -- Document language

  -- Status and metadata
  attachment_type TEXT DEFAULT 'manual', -- 'generated', 'manual'
  status TEXT DEFAULT 'attached', -- 'attached', 'archived', 'deleted'

  -- Audit info
  uploaded_by UUID NOT NULL REFERENCES auth.users(id),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Flexible metadata
  metadata JSONB DEFAULT '{}', -- Generation context, file info, etc.

  -- Constraints
  CONSTRAINT fk_document_attachments_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_document_attachments_template FOREIGN KEY (template_id) REFERENCES document_templates(id),
  CONSTRAINT fk_document_attachments_contact FOREIGN KEY (contact_id) REFERENCES contacts(id),
  CONSTRAINT fk_document_attachments_uploaded_by FOREIGN KEY (uploaded_by) REFERENCES auth.users(id)
);

-- Electronic signatures table
CREATE TABLE document_signatures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  document_id UUID NOT NULL REFERENCES document_attachments(id) ON DELETE CASCADE,
  contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
  signature_status TEXT DEFAULT 'pending', -- 'pending', 'signed', 'rejected', 'expired'
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  signed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  signature_method TEXT DEFAULT 'electronic', -- 'electronic', 'wet', 'digital'
  signature_data TEXT, -- Signature blob/hash/URL
  requested_by UUID NOT NULL REFERENCES auth.users(id),
  metadata JSONB DEFAULT '{}', -- Signature metadata, IP, device info

  -- Constraints
  CONSTRAINT fk_document_signatures_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_document_signatures_document FOREIGN KEY (document_id) REFERENCES document_attachments(id),
  CONSTRAINT fk_document_signatures_contact FOREIGN KEY (contact_id) REFERENCES contacts(id),
  CONSTRAINT fk_document_signatures_requested_by FOREIGN KEY (requested_by) REFERENCES auth.users(id)
);

-- Create indexes for performance
CREATE INDEX idx_document_templates_organization_id ON document_templates(organization_id);
CREATE INDEX idx_document_templates_service_id ON document_templates(service_id);
CREATE INDEX idx_document_templates_template_type ON document_templates(template_type);
CREATE INDEX idx_document_templates_language ON document_templates(language);
CREATE INDEX idx_document_templates_is_active ON document_templates(is_active);

CREATE INDEX idx_document_attachments_organization_id ON document_attachments(organization_id);
CREATE INDEX idx_document_attachments_attached_to ON document_attachments(attached_to_type, attached_to_id);
CREATE INDEX idx_document_attachments_template_id ON document_attachments(template_id);
CREATE INDEX idx_document_attachments_contact_id ON document_attachments(contact_id);
CREATE INDEX idx_document_attachments_attachment_type ON document_attachments(attachment_type);
CREATE INDEX idx_document_attachments_status ON document_attachments(status);

CREATE INDEX idx_document_signatures_organization_id ON document_signatures(organization_id);
CREATE INDEX idx_document_signatures_document_id ON document_signatures(document_id);
CREATE INDEX idx_document_signatures_contact_id ON document_signatures(contact_id);
CREATE INDEX idx_document_signatures_status ON document_signatures(signature_status);
CREATE INDEX idx_document_signatures_requested_by ON document_signatures(requested_by);

-- Create triggers for updated_at
CREATE TRIGGER update_document_templates_updated_at
BEFORE UPDATE ON document_templates
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_document_attachments_updated_at
BEFORE UPDATE ON document_attachments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE document_templates IS 'Document templates with WYSIWYG content and service-based configuration';
COMMENT ON TABLE document_attachments IS 'Universal document attachment system supporting both generated and manual documents';
COMMENT ON TABLE document_signatures IS 'Electronic signature tracking per contact per document';

COMMENT ON COLUMN document_templates.content IS 'WYSIWYG HTML content with token placeholders like {{contact_name}}';
COMMENT ON COLUMN document_templates.metadata IS 'Template configuration and token definitions';
COMMENT ON COLUMN document_attachments.attached_to_type IS 'Entity type: case_file, request, contact, appointment, observation';
COMMENT ON COLUMN document_attachments.attachment_type IS 'Document source: generated from template or manual upload';
COMMENT ON COLUMN document_signatures.signature_data IS 'Signature blob, hash, or URL depending on signature method';