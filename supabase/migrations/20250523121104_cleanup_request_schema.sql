-- Migration to clean up the request schema by removing unnecessary fields
-- This migration removes fields that don't align with business requirements

-- Remove unnecessary fields from the requests table
ALTER TABLE public.requests
DROP COLUMN IF EXISTS title,
DROP COLUMN IF EXISTS description,
DROP COLUMN IF EXISTS priority,
DROP COLUMN IF EXISTS service_type;

-- Remove unnecessary fields from the request_metadata table
ALTER TABLE public.request_metadata
DROP COLUMN IF EXISTS court_judgment_reference,
DROP COLUMN IF EXISTS court_judgment_date,
DROP COLUMN IF EXISTS court_judgment_details,
DROP COLUMN IF EXISTS court_judgment_document_url;

-- Add new columns to the requests table for service details
ALTER TABLE public.requests
ADD COLUMN IF NOT EXISTS duration TEXT,
ADD COLUMN IF NOT EXISTS periodicity TEXT,
ADD COLUMN IF NOT EXISTS frequency_count TEXT;

-- Add comments to document the new fields
COMMENT ON COLUMN public.requests.duration IS 'Duration of each service session in minutes';
COMMENT ON COLUMN public.requests.periodicity IS 'How often the service is required (daily, weekly, biweekly, monthly, custom)';
COMMENT ON COLUMN public.requests.frequency_count IS 'Number of times the service should be repeated per period';

-- Fix the reference number generation function to ensure uniqueness
CREATE OR REPLACE FUNCTION public.generate_request_reference()
RETURNS TRIGGER AS $$
DECLARE
  org_prefix TEXT;
  year_suffix TEXT;
  sequence_num INTEGER;
  ref_number TEXT;
BEGIN
  -- Get organization prefix (first 3 letters of organization name)
  SELECT UPPER(SUBSTRING(name FROM 1 FOR 3)) INTO org_prefix
  FROM organizations
  WHERE id = NEW.organization_id;

  -- If no prefix found, use 'REQ'
  IF org_prefix IS NULL THEN
    org_prefix := 'REQ';
  END IF;

  -- Get year suffix (last 2 digits of current year)
  year_suffix := TO_CHAR(CURRENT_DATE, 'YY');

  -- Get next sequence number for this organization and year
  -- Use a more robust query to ensure uniqueness
  SELECT COALESCE(MAX(CAST(REGEXP_REPLACE(reference_number, '^.*-([0-9]+)$', '\1') AS INTEGER)), 0) + 1 INTO sequence_num
  FROM requests
  WHERE reference_number LIKE org_prefix || '-' || year_suffix || '-%';

  -- Format the reference number: ORG-YY-NNNN
  ref_number := org_prefix || '-' || year_suffix || '-' || LPAD(sequence_num::TEXT, 4, '0');

  -- Set the reference number
  NEW.reference_number := ref_number;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update the status values and constraints
-- First, drop the existing trigger that enforces status transitions
DROP TRIGGER IF EXISTS validate_request_status_transition_trigger ON public.requests;

-- Delete existing status definitions
DELETE FROM public.request_status_definitions;

-- Insert new status definitions with updated values
INSERT INTO public.request_status_definitions (status, description, required_fields, allowed_transitions)
VALUES
    ('draft',
     'Initial state during wizard creation',
     '["organization_id"]'::jsonb,
     '["processing"]'::jsonb),

    ('processing',
     'Request has been submitted and is being processed',
     '["organization_id", "requester_id", "location_id", "service_id", "start_date", "end_date", "duration", "periodicity", "frequency_count"]'::jsonb,
     '["waitlist", "completed", "closed"]'::jsonb),

    ('waitlist',
     'Service cannot be provided immediately',
     '["organization_id", "requester_id", "location_id", "service_id", "start_date", "end_date", "duration", "periodicity", "frequency_count", "waitlist_position"]'::jsonb,
     '["processing", "completed", "closed"]'::jsonb),

    ('completed',
     'Service can be provided, case file initiated',
     '["organization_id", "requester_id", "location_id", "service_id", "start_date", "end_date", "duration", "periodicity", "frequency_count"]'::jsonb,
     '["closed"]'::jsonb),

    ('closed',
     'Request has been manually closed',
     '["organization_id", "requester_id"]'::jsonb,
     '[]'::jsonb)
ON CONFLICT (status) DO UPDATE
SET
    description = EXCLUDED.description,
    required_fields = EXCLUDED.required_fields,
    allowed_transitions = EXCLUDED.allowed_transitions,
    updated_at = now();

-- Recreate the trigger to validate status transitions
CREATE TRIGGER validate_request_status_transition_trigger
BEFORE UPDATE OF status ON public.requests
FOR EACH ROW
EXECUTE FUNCTION public.validate_request_status_transition();