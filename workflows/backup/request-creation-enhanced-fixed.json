{"name": "Request Creation (Enhanced - Fixed)", "nodes": [{"parameters": {"mode": "raw", "jsonOutput": "=\nconst payload = items[0].json;\n\n// Check if payload and payload.body exist\nif (!payload || !payload.body) {\n  console.error('Invalid payload structure:', JSON.stringify(payload, null, 2));\n  return [{ \n    json: { \n      requestData: {\n        title: 'Untitled Request',\n        description: 'No description provided',\n        status: 'processing',\n        priority: 'medium'\n      },\n      metadataData: {},\n      relatedContacts: [],\n      error: 'Invalid payload structure' \n    } \n  }];\n}\n\n// Extract related contacts for later use\nconst relatedContacts = payload.body.data?.relatedContacts || [];\n\n// Parse contact availability if it exists and is a string\nlet contactAvailability = null;\nif (payload.body.data?.contact_availability) {\n  try {\n    if (typeof payload.body.data.contact_availability === 'string') {\n      contactAvailability = JSON.parse(payload.body.data.contact_availability);\n    } else {\n      contactAvailability = payload.body.data.contact_availability;\n    }\n  } catch (e) {\n    console.error('Error parsing contact_availability:', e);\n    contactAvailability = payload.body.data.contact_availability;\n  }\n}\n\n// Create the formatted request object with only fields that belong in the requests table\nconst requestData = {\n  // Basic fields for the requests table\n  organization_id: payload.body.organization_id,\n  requester_id: payload.body.user_id,\n  status: 'processing',\n  \n  // Location and service\n  location_id: payload.body.data?.location_id || null,\n  service_id: payload.body.data?.service_id || null,\n  \n  // Date fields\n  start_date: payload.body.data?.start_date || null,\n  end_date: payload.body.data?.end_date || null,\n  \n  // Service details (now directly in the requests table)\n  duration: payload.body.data?.duration || null,\n  periodicity: payload.body.data?.periodicity || null,\n  frequency_count: payload.body.data?.frequency_count || null,\n  \n  // Contact information (will be removed in future migration)\n  contact_id: payload.body.data?.contact_id || null,\n  relationship_type: payload.body.data?.relationship_type || null\n};\n\n// Create the metadata object for the request_metadata table\nconst metadataData = {\n  // Will be filled with request_id after request creation\n  family_availability: contactAvailability\n};\n\n// Log the data for debugging\nconsole.log('Request data prepared:', JSON.stringify(requestData, null, 2));\nconsole.log('Metadata prepared:', JSON.stringify(metadataData, null, 2));\nconsole.log('Related contacts:', JSON.stringify(relatedContacts, null, 2));\n\n// Return the formatted data and related contacts\nreturn [{ \n  json: {\n    requestData,\n    metadataData,\n    relatedContacts\n  }\n}];"}, "id": "1a1c9c9e-e7c9-4c2c-a7a3-e2e139d18e2c", "name": "Prepare Request Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-80, 300]}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Extract the request data\nconst data = items[0].json;\n\n// Check if we have valid request data\nif (!data || !data.requestData) {\n  console.error('Missing request data');\n  return [{ json: { error: 'Missing request data' } }];\n}\n\nconst requestData = data.requestData;\n\n// Add validation for required fields\nif (!requestData.organization_id) {\n  console.error('Missing required field: organization_id');\n  return [{ json: { error: 'Missing required field: organization_id' } }];\n}\n\n// Return just the request data for the Supabase node\nreturn [{ json: requestData }];"}, "id": "c9f4a0c7-1d7b-4893-9a6a-09c7d462f1c0", "name": "Extract Request Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [140, 300]}, {"parameters": {"operation": "insert", "table": "requests", "schema": "public", "additionalFields": {"data": "={{ $json }}"}}, "id": "a1b2c3d4-e5f6-7890-a1b2-c3d4e5f67890", "name": "Create Request Record", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [360, 300], "credentials": {"supabaseApi": {"id": "1", "name": "Supabase account"}}}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Extract the request ID from the created record\nconst requestId = items[0].json[0].id;\n\n// Get the metadata from the original input\nconst metadataData = $('Prepare Request Data').item.json.metadataData || {};\n\n// Add the request_id to the metadata\nmetadataData.request_id = requestId;\n\n// Log for debugging\nconsole.log('Creating metadata for request:', requestId);\nconsole.log('Metadata data:', JSON.stringify(metadataData, null, 2));\n\n// Return the metadata data for the Supabase node\nreturn [{ json: metadataData }];"}, "id": "b2c3d4e5-f6a7-8901-b2c3-d4e5f6a78901", "name": "Create Request Metadata", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [580, 300]}, {"parameters": {"operation": "insert", "table": "request_metadata", "schema": "public", "additionalFields": {"data": "={{ $json }}"}}, "id": "c3d4e5f6-a7b8-9012-c3d4-e5f6a7b89012", "name": "Create Metadata Record", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [800, 300], "credentials": {"supabaseApi": {"id": "1", "name": "Supabase account"}}}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Extract the request ID from the created record\nconst requestId = items[0].json[0].id;\n\n// Get the related contacts from the original input\nconst relatedContacts = $('Prepare Request Data').item.json.relatedContacts || [];\n\n// Log for debugging\nconsole.log('Request ID:', requestId);\nconsole.log('Related contacts:', JSON.stringify(relatedContacts, null, 2));\n\n// If there are no related contacts, return an empty object\nif (!relatedContacts.length) {\n  console.log('No related contacts to process');\n  return {\n    json: {\n      requestId,\n      relatedContacts: [],\n      message: 'No related contacts to process'\n    }\n  };\n}\n\n// Return the request ID and related contacts for processing\nreturn {\n  json: {\n    requestId,\n    relatedContacts,\n    message: `Found ${relatedContacts.length} related contacts to process`\n  }\n};"}, "id": "d4e5f6a7-b8c9-0123-d4e5-f6a7b8c90123", "name": "Extract Related Contacts", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1020, 300]}, {"parameters": {"workflowId": "ExJiiEtCksRpM0du"}, "id": "e5f6a7b8-c9d0-1234-e5f6-a7b8c9d01234", "name": "Process Related Contacts", "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1, "position": [1240, 300]}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Get the request data\nconst requestData = $('Create Request Record').item.json[0];\nconst requestId = requestData.id;\n\n// Create a success response\nreturn {\n  json: {\n    success: true,\n    message: 'Request created successfully',\n    result: {\n      id: requestId,\n      status: 'processing',\n      reference_number: requestData.reference_number || null\n    },\n    notification: {\n      title: 'Request Created',\n      message: `Request has been created successfully with reference number ${requestData.reference_number || 'N/A'}`\n    }\n  }\n};"}, "id": "f6a7b8c9-d0e1-2345-f6a7-b8c9d0e12345", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1460, 300]}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Get the error message\nconst errorMessage = $input.all()[0].json.message || $input.all()[0].json.error || 'An error occurred while creating the request';\n\n// Create an error response\nreturn {\n  json: {\n    success: false,\n    message: errorMessage,\n    result: {\n      error: errorMessage\n    },\n    notification: {\n      title: 'Request Creation Failed',\n      message: errorMessage\n    }\n  }\n};"}, "id": "a7b8c9d0-e1f2-3456-a7b8-c9d0e1f23456", "name": "Error Response", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [800, 500]}], "connections": {"Prepare Request Data": {"main": [[{"node": "Extract Request Data", "type": "main", "index": 0}]]}, "Extract Request Data": {"main": [[{"node": "Create Request Record", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Create Request Record": {"main": [[{"node": "Create Request Metadata", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Create Request Metadata": {"main": [[{"node": "Create Metadata Record", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Create Metadata Record": {"main": [[{"node": "Extract Related Contacts", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}, "Extract Related Contacts": {"main": [[{"node": "Process Related Contacts", "type": "main", "index": 0}]]}, "Process Related Contacts": {"main": [[{"node": "Success Response", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}}}