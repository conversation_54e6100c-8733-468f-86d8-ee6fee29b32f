{"name": "Execution Complete", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-220, 320], "typeVersion": 1}, {"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.executionId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "completed"}, {"fieldId": "result", "fieldValue": "={{ $json.result }}"}, {"fieldId": "updated_at", "fieldValue": "={{ $now.toUTC() }}"}]}}, "id": "node_2", "name": "Update Status to Completed", "type": "n8n-nodes-base.supabase", "position": [140, 320], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "pinData": {"When Called From Another Workflow": [{"json": {"executionId": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "result": {"id": "57f6da68-5f9b-47b2-b5b9-6a765d7940c3", "organization_id": "********-0000-0000-0000-************", "user_account_id": null, "first_name": "Test", "last_name": "User", "profile_image": null, "date_of_birth": null, "gender": null, "employee_id": null, "hire_date": null, "termination_date": null, "employment_status": "active", "job_title": null, "department": null, "supervisor_id": null, "specializations": null, "certifications": null, "education": null, "emails": [{"type": "work", "email": "<EMAIL>", "primary": true}], "phones": null, "created_at": "2025-05-19T07:33:10.902+00:00", "updated_at": "2025-05-19T07:33:10.902+00:00", "address": null}}}]}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "Update Status to Completed", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0033b549-ae3b-401b-afd5-ac6358af0663", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "O0LORCEXJeFs8F84", "tags": [{"createdAt": "2025-05-19T01:18:30.835Z", "updatedAt": "2025-05-19T01:18:30.835Z", "id": "Dnsl9yLb11FuJixk", "name": "Execution"}]}