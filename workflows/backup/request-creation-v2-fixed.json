{"name": "Request Creation V2 (Fixed)", "nodes": [{"parameters": {"inputSource": "jsonExample"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-240, 240], "id": "008dba70-112d-47c3-bbdc-75463cde207e", "name": "When Executed by Another Workflow"}, {"parameters": {"mode": "raw", "jsonOutput": "=\nconst payload = items[0].json;\n\n// Check if payload and payload.body exist\nif (!payload || !payload.body) {\n  console.error('Invalid payload structure:', JSON.stringify(payload, null, 2));\n  return [{ \n    json: { \n      requestData: {\n        organization_id: payload?.organization_id || '',\n        requester_id: payload?.user_id || '',\n        status: 'processing',\n        location_id: null,\n        service_id: null,\n        start_date: null,\n        end_date: null,\n        duration: null,\n        periodicity: null,\n        frequency_count: null\n      },\n      metadataData: {},\n      relatedContacts: [],\n      error: 'Invalid payload structure' \n    } \n  }];\n}\n\n// Extract related contacts for later use\nconst relatedContacts = payload.body.data?.relatedContacts || [];\n\n// Parse contact availability if it exists and is a string\nlet contactAvailability = null;\nif (payload.body.data?.contact_availability) {\n  try {\n    if (typeof payload.body.data.contact_availability === 'string') {\n      contactAvailability = JSON.parse(payload.body.data.contact_availability);\n    } else {\n      contactAvailability = payload.body.data.contact_availability;\n    }\n  } catch (e) {\n    console.error('Error parsing contact_availability:', e);\n    contactAvailability = payload.body.data.contact_availability;\n  }\n}\n\n// Create the formatted request object with only fields that belong in the requests table\nconst requestData = {\n  // Basic fields for the requests table\n  organization_id: payload.body.organization_id,\n  requester_id: payload.body.user_id,\n  status: 'processing',\n  \n  // Location and service\n  location_id: payload.body.data?.location_id || null,\n  service_id: payload.body.data?.service_id || null,\n  \n  // Date fields\n  start_date: payload.body.data?.start_date || null,\n  end_date: payload.body.data?.end_date || null,\n  \n  // Service details (now directly in the requests table)\n  duration: payload.body.data?.duration || null,\n  periodicity: payload.body.data?.periodicity || null,\n  frequency_count: payload.body.data?.frequency_count || null\n};\n\n// Create the metadata object for the request_metadata table\nconst metadataData = {\n  // Will be filled with request_id after request creation\n  family_availability: contactAvailability\n};\n\n// Log the data for debugging\nconsole.log('Request data prepared:', JSON.stringify(requestData, null, 2));\nconsole.log('Metadata prepared:', JSON.stringify(metadataData, null, 2));\nconsole.log('Related contacts:', JSON.stringify(relatedContacts, null, 2));\n\n// Return the formatted data and related contacts\nreturn [{ \n  json: {\n    requestData,\n    metadataData,\n    relatedContacts\n  }\n}];", "options": {}}, "id": "4863ac6d-bb57-4960-9264-477a2e97f909", "name": "Prepare Request Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-20, 240]}, {"parameters": {"tableId": "requests", "dataToSend": "defineBelow", "columns": {"parameters": [{"column": "organization_id", "value": "={{ $json.requestData.organization_id }}"}, {"column": "requester_id", "value": "={{ $json.requestData.requester_id }}"}, {"column": "status", "value": "={{ $json.requestData.status }}"}, {"column": "location_id", "value": "={{ $json.requestData.location_id }}"}, {"column": "service_id", "value": "={{ $json.requestData.service_id }}"}, {"column": "start_date", "value": "={{ $json.requestData.start_date }}"}, {"column": "end_date", "value": "={{ $json.requestData.end_date }}"}, {"column": "duration", "value": "={{ $json.requestData.duration }}"}, {"column": "periodicity", "value": "={{ $json.requestData.periodicity }}"}, {"column": "frequency_count", "value": "={{ $json.requestData.frequency_count }}"}]}}, "id": "7e077c35-8c24-4e67-9c3a-8eaf83ff6aa0", "name": "Create Request Record", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [200, 240], "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Extract the request ID from the created record\nconst requestId = items[0].json[0].id;\n\n// Get the metadata from the original input\nconst metadataData = $('Prepare Request Data').item.json.metadataData || {};\n\n// Add the request_id to the metadata\nmetadataData.request_id = requestId;\n\n// Log for debugging\nconsole.log('Creating metadata for request:', requestId);\nconsole.log('Metadata data:', JSON.stringify(metadataData, null, 2));\n\n// Return the metadata data for the Supabase node\nreturn [{ json: metadataData }];", "options": {}}, "id": "548148c5-9628-48e8-812c-039cabd92793", "name": "Create Request Metadata", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [420, 240]}, {"parameters": {"tableId": "request_metadata", "dataToSend": "defineBelow", "columns": {"parameters": [{"column": "request_id", "value": "={{ $json.request_id }}"}, {"column": "family_availability", "value": "={{ $json.family_availability }}"}]}}, "id": "fd45a533-fbae-4680-bb65-df0009fbcb6a", "name": "Create Metadata Record", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [640, 240], "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Extract the request ID from the created record\nconst requestId = items[0].json[0].id;\n\n// Get the related contacts from the original input\nconst relatedContacts = $('Prepare Request Data').item.json.relatedContacts || [];\n\n// Log for debugging\nconsole.log('Request ID:', requestId);\nconsole.log('Related contacts:', JSON.stringify(relatedContacts, null, 2));\n\n// If there are no related contacts, return an empty object\nif (!relatedContacts.length) {\n  console.log('No related contacts to process');\n  return {\n    json: {\n      requestId,\n      relatedContacts: [],\n      message: 'No related contacts to process'\n    }\n  };\n}\n\n// Return the request ID and related contacts for processing\nreturn {\n  json: {\n    requestId,\n    relatedContacts,\n    message: `Found ${relatedContacts.length} related contacts to process`\n  }\n};", "options": {}}, "id": "2feb9f79-f797-4ea8-b900-4ddb10ccfe28", "name": "Extract Related Contacts", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [860, 240]}, {"parameters": {"workflowId": {"__rl": true, "value": "VxQp3vj0csRvU0iv", "mode": "list", "cachedResultName": "Process Request Related Contacts"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": false}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1080, 240], "id": "b7a91a1e-09eb-4ed2-946f-8145e083557c", "name": "Process Related Contacts"}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Get the request data\nconst requestData = $('Create Request Record').item.json[0];\nconst requestId = requestData.id;\n\n// Create a success response\nreturn {\n  json: {\n    success: true,\n    message: 'Request created successfully',\n    result: {\n      id: requestId,\n      status: 'processing',\n      reference_number: requestData.reference_number || null\n    },\n    notification: {\n      title: 'Request Created',\n      message: `Request has been created successfully with reference number ${requestData.reference_number || 'N/A'}`\n    }\n  }\n};", "options": {}}, "id": "c12d830c-e412-4e41-8edb-f6b03ef868ad", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1300, 240]}, {"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [-220, 480], "id": "71fadc46-726c-488f-94dd-c8970ca58474", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"mode": "raw", "jsonOutput": "=\n// Get the error message\nconst errorMessage = $input.all()[0].json.message || 'An error occurred while creating the request';\n\n// Create an error response\nreturn {\n  json: {\n    success: false,\n    message: errorMessage,\n    result: {\n      error: errorMessage\n    },\n    notification: {\n      title: 'Request Creation Failed',\n      message: errorMessage\n    }\n  }\n};", "options": {}}, "id": "795be2ca-611f-4150-aaeb-507ca54c27ee", "name": "Error Response", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [0, 480]}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Prepare Request Data", "type": "main", "index": 0}]]}, "Prepare Request Data": {"main": [[{"node": "Create Request Record", "type": "main", "index": 0}]]}, "Create Request Record": {"main": [[{"node": "Create Request Metadata", "type": "main", "index": 0}]]}, "Create Request Metadata": {"main": [[{"node": "Create Metadata Record", "type": "main", "index": 0}]]}, "Create Metadata Record": {"main": [[{"node": "Extract Related Contacts", "type": "main", "index": 0}]]}, "Extract Related Contacts": {"main": [[{"node": "Process Related Contacts", "type": "main", "index": 0}]]}, "Process Related Contacts": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "tags": [{"name": "Business Process"}]}