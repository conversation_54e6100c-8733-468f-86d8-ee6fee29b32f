{"name": "Workflow Router (Enhanced)", "nodes": [{"parameters": {"httpMethod": "POST", "path": "4db68191-446b-4b5f-bd5d-d9ef9b5de254", "options": {}}, "id": "ec784175-92e7-48b8-a108-904dbe335ba4", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [-200, 540], "typeVersion": 1, "webhookId": "4db68191-446b-4b5f-bd5d-d9ef9b5de254"}, {"parameters": {"workflowId": "O0LORCEXJeFs8F84", "options": {"waitForSubWorkflow": true}}, "id": "096880a2-f5f7-4963-b315-cb9da070f60a", "name": "Complete Execution", "type": "n8n-nodes-base.executeWorkflow", "position": [1400, 480], "typeVersion": 1}, {"parameters": {"workflowId": "PtjU76R6elskGAJm", "options": {"waitForSubWorkflow": true}}, "id": "f44cb791-cbac-4b39-ba47-c2a9e30ab319", "name": "Notification Success", "type": "n8n-nodes-base.executeWorkflow", "position": [1400, 140], "typeVersion": 1}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.body.workflow_type }}", "rightValue": "employee_creation", "operator": {"type": "string", "operation": "contains"}, "id": "898cac8d-2c5c-4fd7-83d3-bd7d1628da22"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Employee Creation"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a52ac15b-5c9b-458f-9d7f-9eaeddd7aac0", "leftValue": "={{ $json.body.workflow_type }}", "rightValue": "request_creation", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Request Creation"}]}, "options": {}}, "id": "513408fb-605d-4f38-ac3a-d34b215e3d15", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [400, 280], "typeVersion": 3.2}, {"parameters": {"workflowId": "zcFVBb9uIXIT9Bnc", "options": {"waitForSubWorkflow": true}}, "id": "ff966e57-573f-4fb5-bb5b-808f209d74c2", "name": "Employee Creation", "type": "n8n-nodes-base.executeWorkflow", "position": [700, -80], "typeVersion": 1}, {"parameters": {"mode": "raw", "jsonOutput": "=\n{\n\"executionId\": \"{{ $('Webhook Trigger').first().json.body.id }}\",\n\"result\": {{ $json.result.toJsonString() }},\n\"title\": \"{{ $json.notification.title }}\",\n\"message\": \"{{ $json.notification.message }}\",\n\"user_id\": \"{{ $('Webhook Trigger').first().json.body.user_id }}\",\n\"organization_id\": \"{{ $('Webhook Trigger').first().json.body.organization_id}}\"\n}\n", "options": {}}, "id": "14eeab07-3a7d-4af0-a7e4-9a94ece7744b", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [1040, 280], "typeVersion": 3.4}, {"parameters": {"workflowId": "ExJiiEtCksRpM0du", "options": {"waitForSubWorkflow": false}}, "id": "9e3e30cf-7ba1-47e9-b83b-7471ff73911e", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [80, 500], "typeVersion": 1}, {"parameters": {"workflowId": "lw4ts8IVjGTBe3gS", "options": {"waitForSubWorkflow": true}}, "id": "a0045f54-4f3b-4f6d-be2c-3346d3c9c998", "name": "Request Creation (Enhanced)", "type": "n8n-nodes-base.executeWorkflow", "position": [700, 480], "typeVersion": 1}], "connections": {"Webhook Trigger": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Employee Creation", "type": "main", "index": 0}], [{"node": "Request Creation (Enhanced)", "type": "main", "index": 0}]]}, "Employee Creation": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Request Creation (Enhanced)": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Complete Execution", "type": "main", "index": 0}, {"node": "Notification Success", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "RF8aJyM6Ga2DjddH"}, "active": false, "versionId": "783c77ff-0807-4197-b89a-6e7b26522d84"}