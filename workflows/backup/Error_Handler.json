{"name": "<PERSON><PERSON><PERSON>", "nodes": [{"parameters": {}, "id": "643dbd46-bab6-489d-907d-1ae37aca1f26", "name": "Catch Errors", "type": "n8n-nodes-base.errorTrigger", "position": [440, 760], "typeVersion": 1}, {"parameters": {"workflowId": {"__rl": true, "value": "5BByydkOieU0V7W5", "mode": "list", "cachedResultName": "Notification Error"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [960, 760], "id": "79e5126a-430b-441e-9cb3-dbb0c547a9e6", "name": "Execute Workflow", "disabled": true}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1280, 760], "id": "980f8902-6875-4fdb-a50e-76da53caf93d", "name": "No Operation, do nothing"}, {"parameters": {"command": "echo \"test\""}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [660, 760], "id": "859d0336-e693-4f11-8292-072a202921fc", "name": "Execute Command"}], "pinData": {}, "connections": {"Catch Errors": {"main": [[{"node": "Execute Command", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Execute Command": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "versionId": "c50914d6-9916-4a9c-a6bf-28fa47f35272", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "RF8aJyM6Ga2DjddH", "tags": [{"createdAt": "2025-05-19T06:13:56.763Z", "updatedAt": "2025-05-19T06:13:56.763Z", "id": "wqpWT2Zz4oQJvRas", "name": "Error"}]}