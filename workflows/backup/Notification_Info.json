{"name": "Notification Info", "nodes": [{"parameters": {}, "id": "2d8a67db-39a4-45cc-9918-ea104ed84114", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-180, 300], "typeVersion": 1}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [740, 420], "id": "1420588c-357e-4b20-a7ce-647fc287cd82", "name": "No Operation, do nothing"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "91462c76-8f8a-4482-9500-b1666d3d1deb", "leftValue": "={{ $json.user_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "12083925-14e5-4d00-9ea0-ad4c35d1f2fe", "leftValue": "={{ $json.organization_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "00afa2dd-893b-48b5-bb86-bbf57e219704", "leftValue": "={{ $json.message }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}, {"id": "345283fe-bcee-469d-8913-59facc1087a2", "leftValue": "={{ $json.title }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [340, 300], "id": "64c32fe9-0571-4604-a327-07e3274827ca", "name": "Validation"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"type\": \"info\",\n  \"title\": \"{{ $json.title }}\",\n  \"message\": \"{{ $json.message }}\",\n  \"user_id\": \"{{ $json.user_id}}\",\n  \"organization_id\": \"{{ $json.organization_id }}\",\n  \"read\": false\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 300], "id": "ff0b23cc-3e7f-431d-bf00-9ab7840f0c1c", "name": "DTO"}, {"parameters": {"tableId": "notifications", "dataToSend": "autoMapInputData"}, "id": "65d741d8-8cfa-4e46-adfd-935bb78727b0", "name": "Create Info Notification", "type": "n8n-nodes-base.supabase", "position": [740, 160], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "pinData": {"When Called From Another Workflow": [{"json": {"title": "Information", "message": "This is a test notification information.", "user_id": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "organization_id": "00000000-0000-0000-0000-000000000001"}}]}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "DTO", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Create Info Notification", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "DTO": {"main": [[{"node": "Validation", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "53d4af4d-5aac-4d7b-80dc-bf66118bfc6e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "PtjU76R6elskGAJm", "tags": [{"createdAt": "2025-05-19T01:17:10.835Z", "updatedAt": "2025-05-19T01:17:10.835Z", "id": "6OnnSnIaUdsuTYbO", "name": "Notification"}]}