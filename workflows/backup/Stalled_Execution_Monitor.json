{"name": "Stalled Execution Monitor", "nodes": [{"parameters": {"rule": {"interval": [{"field": "minutes", "minutesInterval": 15}]}}, "id": "node_1", "name": "Every 15 Minutes", "type": "n8n-nodes-base.scheduleTrigger", "position": [0, 300], "typeVersion": 1}, {"parameters": {"operation": "get", "tableId": "executions", "filters": {"conditions": [{"keyName": "status", "keyValue": "completed"}]}}, "id": "node_2", "name": "Find Running Executions", "type": "n8n-nodes-base.supabase", "position": [220, 300], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "8pvDA5vpbtODIJCI", "name": "Supabase Cloud"}}}, {"parameters": {"functionCode": "// Check if the execution is stalled (updated_at is more than 30 minutes ago)\nconst stalledExecutions = [];\n\nfor (const item of items) {\n  // Parse the updated_at timestamp\n  const updatedAt = new Date(item.json.updated_at);\n  \n  // Calculate the time threshold (30 minutes ago)\n  const thirtyMinutesAgo = new Date();\n  thirtyMinutesAgo.setMinutes(thirtyMinutesAgo.getMinutes() - 30);\n  \n  // If the execution is stalled, add it to the result\n  if (updatedAt < thirtyMinutesAgo) {\n    stalledExecutions.push(item);\n  }\n}\n\nreturn stalledExecutions;"}, "id": "node_3", "name": "Filter Stalled Executions", "type": "n8n-nodes-base.function", "position": [440, 300], "typeVersion": 1}, {"parameters": {"options": {}}, "id": "node_4", "name": "Process Each Execution", "type": "n8n-nodes-base.splitInBatches", "position": [660, 300], "typeVersion": 1}, {"parameters": {"workflowId": "JXUiwrKcj8LhnMYm", "options": {}}, "id": "node_5", "name": "Handle Stalled Execution", "type": "n8n-nodes-base.executeWorkflow", "position": [880, 300], "typeVersion": 1}], "pinData": {}, "connections": {"Every 15 Minutes": {"main": [[{"node": "Find Running Executions", "type": "main", "index": 0}]]}, "Find Running Executions": {"main": [[{"node": "Filter Stalled Executions", "type": "main", "index": 0}]]}, "Filter Stalled Executions": {"main": [[{"node": "Process Each Execution", "type": "main", "index": 0}]]}, "Process Each Execution": {"main": [[{"node": "Handle Stalled Execution", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "88698cac-22db-49e3-b40a-a77e6f60f701", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "9MOQPGmepX04vG8D", "tags": [{"createdAt": "2025-05-19T01:22:02.740Z", "updatedAt": "2025-05-19T01:22:02.740Z", "id": "1R4RTuGtuiSMXlzP", "name": "Scheduled Operation"}]}