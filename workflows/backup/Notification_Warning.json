{"name": "Notification Warning", "nodes": [{"parameters": {}, "id": "d4df4ae9-52de-4437-9790-45efa669ee83", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-180, 300], "typeVersion": 1}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [740, 420], "id": "b7a9b738-0eee-4348-a191-140feecd7ea2", "name": "No Operation, do nothing"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "91462c76-8f8a-4482-9500-b1666d3d1deb", "leftValue": "={{ $json.user_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "12083925-14e5-4d00-9ea0-ad4c35d1f2fe", "leftValue": "={{ $json.organization_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "00afa2dd-893b-48b5-bb86-bbf57e219704", "leftValue": "={{ $json.message }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}, {"id": "345283fe-bcee-469d-8913-59facc1087a2", "leftValue": "={{ $json.title }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [340, 300], "id": "7834721e-08e6-4555-ac77-02275f2aa3af", "name": "Validation"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"type\": \"warnoing\",\n  \"title\": \"{{ $json.title }}\",\n  \"message\": \"{{ $json.message }}\",\n  \"user_id\": \"{{ $json.user_id}}\",\n  \"organization_id\": \"{{ $json.organization_id }}\",\n  \"read\": false\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 300], "id": "3027b849-2719-40d4-b99b-227993202ba6", "name": "DTO"}, {"parameters": {"tableId": "notifications", "dataToSend": "autoMapInputData"}, "id": "a08e2133-e7aa-48a5-93d7-122cb1189d5a", "name": "Create Warning Notification", "type": "n8n-nodes-base.supabase", "position": [740, 160], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "pinData": {"When Called From Another Workflow": [{"json": {"title": "Notification Warning", "message": "This is a test notification warning.", "user_id": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "organization_id": "00000000-0000-0000-0000-000000000001"}}]}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "DTO", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Create Warning Notification", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "DTO": {"main": [[{"node": "Validation", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "4960b6db-3a11-415c-bb0b-00c9349f5fd0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "0z7gxjHK6taJ4vTv", "tags": [{"createdAt": "2025-05-19T01:17:10.835Z", "updatedAt": "2025-05-19T01:17:10.835Z", "id": "6OnnSnIaUdsuTYbO", "name": "Notification"}]}