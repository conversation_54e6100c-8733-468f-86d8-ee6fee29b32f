{"name": "Process Request Related Contacts", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [0, 300], "typeVersion": 1}, {"parameters": {"functionCode": "// Extract the request ID and related contacts from the input\nconst input = items[0].json;\nconst requestId = input.requestId;\nconst relatedContacts = input.relatedContacts || [];\n\n// If there are no related contacts, return an empty array\nif (!relatedContacts.length) {\n  console.log('No related contacts to process');\n  return [];\n}\n\n// Create an array of request_contacts records\nconst requestContacts = relatedContacts.map(contact => ({\n  json: {\n    request_id: requestId,\n    contact_id: contact.id,\n    relationship_type: contact.relationshipType,\n  }\n}));\n\nconsole.log(`Processing ${requestContacts.length} related contacts for request ${requestId}`);\n\nreturn requestContacts;"}, "id": "node_2", "name": "Process Related Contacts", "type": "n8n-nodes-base.function", "position": [200, 300], "typeVersion": 1}, {"parameters": {"tableId": "request_contacts", "dataToSend": "autoMapInputData"}, "id": "node_3", "name": "Create Request Contacts", "type": "n8n-nodes-base.supabase", "position": [400, 300], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"functionCode": "// Create a success response\nreturn {\n  json: {\n    success: true,\n    message: \"Related contacts processed successfully\",\n    requestId: items[0].json.request_id,\n    contactsProcessed: items.length\n  }\n};"}, "id": "node_4", "name": "Success Response", "type": "n8n-nodes-base.function", "position": [600, 200], "typeVersion": 1}, {"parameters": {"functionCode": "// Create an error response\nreturn {\n  json: {\n    success: false,\n    message: `Error processing related contacts: ${items[0].json.error || 'Unknown error'}`,\n    requestId: $('Process Related Contacts').first().json.request_id\n  }\n};"}, "id": "node_5", "name": "Error Response", "type": "n8n-nodes-base.function", "position": [600, 400], "typeVersion": 1}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Process Related Contacts", "type": "main", "index": 0}]]}, "Process Related Contacts": {"main": [[{"node": "Create Request Contacts", "type": "main", "index": 0}]]}, "Create Request Contacts": {"main": [[{"node": "Success Response", "type": "main", "index": 0}], [{"node": "Error Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "tags": [{"name": "Operation"}]}