# n8n Workflows

This directory contains the JSON definitions for n8n workflows used in the application.

## Workflow Diagram
![Workflow Diagram](./workflow_diagram_handwritten.png)

## Workflow Files

### Business Process Workflows
- `request-creation-working.json` - The current working version of the request creation workflow
- `employee-creation.json` - Creates employee records
- `process-request-related-contacts.json` - Processes related contacts for requests

### Status Workflows
- `status-running-node.json` - Updates execution status to "running"
- `status-error-node.json` - Updates execution status to "error"
- `execution-complete.json` - Updates execution status to "complete"

### Notification Workflows
- `notification-error.json` - Creates error notifications
- `notification-warning.json` - Creates warning notifications
- `notification-info.json` - Creates info notifications

### Monitoring Workflows
- `stalled-execution-monitor.json` - Monitors for stalled executions

### Routing Workflows
- `workflow-router.json` - Routes webhook calls to appropriate workflows

## Usage

To import these workflows into n8n:

1. Open n8n
2. Go to Workflows
3. Click "Import from File"
4. Select the workflow JSON file
5. Review and save the workflow

## Workflow Dependencies

Some workflows depend on others:

- The Router workflow calls the Request Creation and Employee Creation workflows
- Status workflows are called by various business process workflows
- Notification workflows are called by business process workflows

## Credentials

These workflows require Supabase credentials to be configured in n8n.
