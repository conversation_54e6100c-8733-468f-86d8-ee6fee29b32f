{"name": "Employee Creation", "nodes": [{"parameters": {}, "id": "0687e86e-41c2-46ee-876a-d0cdd8849119", "name": "When Executed by Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-380, 240], "typeVersion": 1}, {"parameters": {"functionCode": "const payload = items[0].json;\n// Create the formatted employee object\nconst employeeData = {\n first_name: payload.firstName,\n  last_name: payload.lastName,\n  employment_status: \"active\",\n  user_account_id: payload.id,\n  organization_id: payload.organizationId,\n  job_title: $('When Executed by Another Workflow').first().json.body.data.job_title,\n  emails: [{\n    type: 'work',\n    email: payload.email,\n    primary: true\n  }],\n};\n\n// Return the formatted data\nreturn [{ json: employeeData }];"}, "id": "node_2", "name": "Prepare Employee Data", "type": "n8n-nodes-base.function", "position": [160, 460], "typeVersion": 1}, {"parameters": {"tableId": "employees", "fieldsUi": {"fieldValues": [{"fieldId": "organization_id", "fieldValue": "={{ $json.organization_id }}"}, {"fieldId": "user_account_id", "fieldValue": "={{ $json.user_account_id }}"}, {"fieldId": "first_name", "fieldValue": "={{ $json.first_name }}"}, {"fieldId": "last_name", "fieldValue": "={{ $json.last_name }}"}, {"fieldId": "employment_status", "fieldValue": "={{ $json.employment_status }}"}, {"fieldId": "emails", "fieldValue": "={{ $json.emails }}"}, {"fieldId": "job_title", "fieldValue": "={{ $json.job_title }}"}]}}, "id": "node_4", "name": "Create Employee Record", "type": "n8n-nodes-base.supabase", "position": [400, 280], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}, "onError": "continueErrorOutput"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n\"result\": {{ $json.toJsonString() }},\n\"notification\":{\n\"tile\": \"{{ $workflow.name }}\",\n\"message\":\"The employee was creared successfully.\"\n}\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [740, 180], "id": "bff504a0-442c-4395-8acc-f0fc487cb1bb", "name": "<PERSON>"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n\"result\": {{ $json.toJsonString() }},\n\"notification\":{\n\"tile\": \"{{ $workflow.name }}\",\n\"message\":\"{{ $json.error }}\"\n}\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [800, 460], "id": "0269dbcf-a107-4f56-80c9-233caeb7bd88", "name": "Edit Fields1"}, {"parameters": {"method": "POST", "url": "http://172.20.220.143:3001/api/users", "sendBody": true, "bodyParameters": {"parameters": [{"name": "role", "value": "={{ $json.body.data.role }}"}, {"name": "email", "value": "={{ $json.body.data.email }}"}, {"name": "lastName", "value": "={{ $json.body.data.last_name }}"}, {"name": "firstName", "value": "={{ $json.body.data.first_name }}"}, {"name": "organizationId", "value": "={{ $json.body.organization_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-100, 320], "id": "880376f0-17ba-407b-afb6-bef69e3f7ebb", "name": "HTTP Request"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Prepare Employee Data": {"main": [[{"node": "Create Employee Record", "type": "main", "index": 0}]]}, "Create Employee Record": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}], [{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Prepare Employee Data", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "RF8aJyM6Ga2DjddH"}, "tags": [{"name": "Business Process"}]}