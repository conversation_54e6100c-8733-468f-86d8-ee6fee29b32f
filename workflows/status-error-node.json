{"name": "Status Error Node", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [0, 300], "typeVersion": 1}, {"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.executionId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "failed"}, {"fieldId": "error", "fieldValue": "={{ $json.error }}"}, {"fieldId": "completed_at", "fieldValue": "={{ $now.toISOString() }}"}, {"fieldId": "updated_at", "fieldValue": "={{ $now.toISOString() }}"}]}}, "id": "node_2", "name": "Update Status to Failed", "type": "n8n-nodes-base.supabase", "position": [200, 300], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "connections": {"When Called From Another Workflow": {"main": [[{"node": "Update Status to Failed", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "tags": [{"name": "Execution"}]}