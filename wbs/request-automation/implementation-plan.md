# Request Creation Automation Implementation Plan

## Progress Tracking

- After completing each step, update the checklist by changing `[ ]` to `[x]`
- Review the plan after each phase to ensure we're on track
- If we go off track, realign with the plan before continuing
- Keep the scope focused on the essential features for the MVP
- If any blocker or issue arises, stop immediately and discuss it before proceeding
- Don't make assumptions about how to resolve issues - always consult first

## Phase 1: Wizard Configuration and Entry Point

- [x] 1.1. Create request wizard configuration
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/config/requestWizardConfigData.ts`
  - [x] Define steps: basic_info, service_requirements, family_availability, review
  - [x] Run build to check for errors: `npm run build`

- [x] 1.2. Register wizard configuration
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/config/requestWizardConfig.ts`
  - [x] Register with existing wizard registry
  - [x] Run build to check for errors: `npm run build`

- [x] 1.3. Create entry point page
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/page.tsx`
  - [x] Implement createRequestDraft action
  - [x] Run build to check for errors: `npm run build`
  - [x] Test navigation to the page

## Phase 2: Step Components Implementation

- [x] 2.1. Create Basic Info Step
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/BasicInfoStep.tsx`
  - [x] Implement form with title, description, service_type, priority fields
  - [x] Run build to check for errors: `npm run build`
  - [x] Test the step in the browser

- [x] 2.2. Create Service Requirements Step
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/ServiceRequirementsStep.tsx`
  - [x] Implement form with frequency, duration, special requirements fields
  - [x] Run build to check for errors: `npm run build`
  - [x] Test the step in the browser

- [x] 2.3. Create Family Availability Step
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/FamilyAvailabilityStep.tsx`
  - [x] Implement form with availability slots, preferred times
  - [x] Run build to check for errors: `npm run build`
  - [x] Test the step in the browser

- [x] 2.4. Create Review Step
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/ReviewStep.tsx`
  - [x] Display summary of all information
  - [x] Run build to check for errors: `npm run build`
  - [x] Test the step in the browser

## Phase 3: Server Actions Implementation

- [x] 3.1. Create Request Draft Action
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/actions/createRequestDraft.ts`
  - [x] Implement draft creation with request-specific data structure
  - [x] Run build to check for errors: `npm run build`
  - [x] Test draft creation

- [x] 3.2. Complete Request Action
  - [x] Create `src/app/[lang]/protected/automation/(features)/request-wizard/actions/completeRequest.ts`
  - [x] Implement final submission logic
  - [x] Run build to check for errors: `npm run build`
  - [x] Test request completion

## Phase 4: n8n Workflow Implementation

- [x] 4.1. Create Request Creation Workflow
  - [x] Create new workflow in n8n
  - [x] Implement nodes for processing request data
  - [x] Connect to existing workflow router
  - [x] Test workflow execution manually

- [x] 4.2. Test End-to-End Flow
  - [x] Test complete flow from wizard to database
  - [x] Verify all data is correctly saved
  - [x] Test with different input values

## Implementation Notes

### Data Structure
```typescript
// Draft data structure
{
  id: string;
  workflow_type: "request_creation";
  current_step: "basic_info" | "service_requirements" | "family_availability" | "review";
  data: {
    // Basic Info
    title: string;
    description: string;
    service_type: string;
    priority: string;

    // Service Requirements
    frequency: string;
    duration: number;
    special_requirements: string[];
    preferred_days: string[];
    preferred_time_of_day: string;
    service_notes: string;

    // Family Availability
    general_availability: string;
    availability_slots: TimeSlot[];
    availability_notes: string;
  };
}
```

### Key Files to Reference
- Employee wizard: `src/app/[lang]/protected/automation/(features)/employee-wizard/`
- Wizard framework: `src/app/[lang]/protected/automation/lib/`
- Request schema: `supabase/migrations/20250518052620_create_request_tables.sql`
