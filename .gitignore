# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Supabase
.supabase/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs
*.log

# Temporary directories
/temp-*
.env.local.bak
~/.vscode-server/data/Machine/settings.json
~/.vscode-server/data/User/settings.json
.augment.json
