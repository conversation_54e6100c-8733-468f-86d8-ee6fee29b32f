import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { viewTemplate, getAvailableTokens } from "../../../actions/view";
import { TemplateForm } from "../../../components/TemplateForm";

interface TemplateEditPageProps {
  params: {
    id: string;
  };
}

export default async function TemplateEditPage({ params }: TemplateEditPageProps) {
  // Get template data and available tokens in parallel
  const [templateResult, tokensResult] = await Promise.all([
    viewTemplate(params.id),
    getAvailableTokens()
  ]);

  if (!templateResult.success) {
    notFound();
  }

  const template = templateResult.data;
  const availableTokens = tokensResult.success ? tokensResult.data : [];

  if (!template) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="../view">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Template
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Template</h1>
          <p className="text-muted-foreground">
            Modify "{template.name}" template
          </p>
        </div>
      </div>

      {/* Template Form */}
      <Suspense fallback={<div className="p-6">Loading form...</div>}>
        <TemplateForm 
          template={template}
          mode="edit" 
          availableTokens={availableTokens}
        />
      </Suspense>
    </div>
  );
}
