"use server";

import { revalidatePath, redirect } from "next/cache";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { getCurrentUser } from "@/lib/authentication/services/AuthenticationService";
import { getUserProfile } from "@/lib/authorization/services/RoleService";

/**
 * Delete a document template (with redirect)
 */
export async function deleteTemplate(id: string) {
  try {
    // Get current user and profile
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      throw new Error("Authentication required");
    }

    const userProfile = await getUserProfile(currentUser.id);
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);
    
    if (!existingResult.success) {
      throw new Error("Template not found");
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      throw new Error("Template not found");
    }

    // Delete template using service
    const result = await templateService.deleteTemplate(id);

    if (!result.success) {
      throw new Error(result.message);
    }

    // Revalidate and redirect
    revalidatePath("/protected/document/templates");
    redirect("/protected/document/templates/list");

  } catch (error) {
    throw new Error(error instanceof Error ? error.message : "Failed to delete template");
  }
}

/**
 * Delete a document template (without redirect)
 */
export async function deleteTemplateAction(id: string) {
  try {
    // Get current user and profile
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await getUserProfile(currentUser.id);
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);
    
    if (!existingResult.success) {
      return { success: false, error: "Template not found" };
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Template not found" };
    }

    // Delete template using service
    const result = await templateService.deleteTemplate(id);

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates list
    revalidatePath("/protected/document/templates");

    return { 
      success: true, 
      message: "Template deleted successfully" 
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete template"
    };
  }
}
