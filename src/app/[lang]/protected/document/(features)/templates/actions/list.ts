"use server";

import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { DocumentTemplateFilters } from "../lib/types";
import { getCurrentUser } from "@/lib/authentication/services/AuthenticationService";
import { getUserProfile } from "@/lib/authorization/services/RoleService";

/**
 * List document templates with filtering and pagination
 */
export async function listTemplates(filters: DocumentTemplateFilters = {}) {
  try {
    // Get current user and profile
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return {
        success: false,
        message: "Authentication required",
      };
    }

    const userProfile = await getUserProfile(currentUser.id);
    if (!userProfile?.organizationId) {
      return {
        success: false,
        message: "Organization context required",
      };
    }

    // Get templates using service
    const templateService = new DocumentTemplateService();
    const result = await templateService.listTemplates(userProfile.organizationId, filters);

    if (!result.success) {
      return {
        success: false,
        message: result.message,
      };
    }

    return {
      success: true,
      data: result.data || undefined,
      message: "Templates fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to fetch templates",
    };
  }
}
