"use server";

import { revalidatePath, redirect } from "next/cache";
import { z } from "zod";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { getCurrentUser } from "@/lib/authentication/services/AuthenticationService";
import { getUserProfile } from "@/lib/authorization/services/RoleService";
import { TEMPLATE_CATEGORIES } from "../lib/types";

// Validation schema
const createTemplateSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  category: z.enum(TEMPLATE_CATEGORIES, { required_error: "Category is required" }),
  tags: z.array(z.string()).optional(),
  is_active: z.boolean().optional().default(true)
});

export type CreateTemplateFormState = {
  success: boolean;
  message: string;
  errors?: Record<string, string[]>;
};

/**
 * Create a new document template (with redirect)
 */
export async function createTemplate(
  prevState: CreateTemplateFormState,
  formData: FormData
): Promise<CreateTemplateFormState> {
  try {
    // Get current user and profile
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      throw new Error("Authentication required");
    }

    const userProfile = await getUserProfile(currentUser.id);
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name"),
      description: formData.get("description") || undefined,
      content: formData.get("content"),
      category: formData.get("category"),
      tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : [],
      is_active: formData.get("is_active") === "true"
    };

    const validatedData = createTemplateSchema.parse(rawData);

    // Validate template content
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(validatedData.content);
    
    if (!validation.isValid) {
      return {
        success: false,
        message: "Template content validation failed",
        errors: { content: validation.errors }
      };
    }

    // Create template using service
    const result = await templateService.createTemplate({
      name: validatedData.name,
      description: validatedData.description || null,
      content: validatedData.content,
      category: validatedData.category,
      tags: validatedData.tags || [],
      is_active: validatedData.is_active,
      organization_id: userProfile.organizationId,
      created_by: currentUser.id,
      updated_by: currentUser.id
    });

    if (!result.success) {
      return {
        success: false,
        message: result.message
      };
    }

    // Revalidate and redirect
    revalidatePath("/protected/document/templates");
    redirect(`/protected/document/templates/${result.data?.id}/view`);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Validation failed",
        errors: error.flatten().fieldErrors
      };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create template"
    };
  }
}

/**
 * Create a new document template (without redirect)
 */
export async function createTemplateAction(formData: FormData) {
  try {
    // Get current user and profile
    const currentUser = await getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await getUserProfile(currentUser.id);
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name"),
      description: formData.get("description") || undefined,
      content: formData.get("content"),
      category: formData.get("category"),
      tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : [],
      is_active: formData.get("is_active") === "true"
    };

    const validatedData = createTemplateSchema.parse(rawData);

    // Validate template content
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(validatedData.content);
    
    if (!validation.isValid) {
      return {
        success: false,
        error: "Template content validation failed: " + validation.errors.join(", ")
      };
    }

    // Create template using service
    const result = await templateService.createTemplate({
      name: validatedData.name,
      description: validatedData.description || null,
      content: validatedData.content,
      category: validatedData.category,
      tags: validatedData.tags || [],
      is_active: validatedData.is_active,
      organization_id: userProfile.organizationId,
      created_by: currentUser.id,
      updated_by: currentUser.id
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates list
    revalidatePath("/protected/document/templates");

    return { 
      success: true, 
      data: result.data,
      message: "Template created successfully" 
    };

  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = Object.entries(error.flatten().fieldErrors)
        .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
        .join("; ");
      return { success: false, error: `Validation failed: ${errorMessages}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create template"
    };
  }
}
