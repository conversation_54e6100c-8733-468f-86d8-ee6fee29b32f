"use client";

import { useState, useEffect } from "react";
import { useFormState } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, X, Eye, Code } from "lucide-react";
import { 
  DocumentTemplateWithMetadata, 
  TEMPLATE_CATEGORIES, 
  TemplateTokenGroup,
  CreateTemplateFormState,
  UpdateTemplateFormState 
} from "../lib/types";
import { createTemplate } from "../actions/create";
import { updateTemplate } from "../actions/update";

interface TemplateFormProps {
  template?: DocumentTemplateWithMetadata;
  availableTokens?: TemplateTokenGroup[];
  mode: 'create' | 'edit';
}

const initialCreateState: CreateTemplateFormState = {
  success: false,
  message: ""
};

const initialUpdateState: UpdateTemplateFormState = {
  success: false,
  message: ""
};

export function TemplateForm({ template, availableTokens = [], mode }: TemplateFormProps) {
  const [createState, createAction] = useFormState(createTemplate, initialCreateState);
  const [updateState, updateAction] = useFormState(
    mode === 'edit' && template ? updateTemplate.bind(null, template.id) : () => initialUpdateState,
    initialUpdateState
  );

  const [tags, setTags] = useState<string[]>(template?.tags || []);
  const [newTag, setNewTag] = useState("");
  const [content, setContent] = useState(template?.content || "");
  const [isActive, setIsActive] = useState(template?.is_active ?? true);
  const [previewMode, setPreviewMode] = useState<'editor' | 'preview'>('editor');

  const state = mode === 'create' ? createState : updateState;
  const action = mode === 'create' ? createAction : updateAction;

  // Handle tag management
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  // Insert token into content
  const insertToken = (tokenKey: string) => {
    const tokenText = `{{ ${tokenKey} }}`;
    setContent(prev => prev + tokenText);
  };

  return (
    <form action={action} className="space-y-6">
      {/* Error/Success Messages */}
      {state.message && (
        <Alert variant={state.success ? "default" : "destructive"}>
          <AlertDescription>{state.message}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Template Name *</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={template?.name}
                  placeholder="Enter template name"
                  required
                />
                {state.errors?.name && (
                  <p className="text-sm text-destructive mt-1">{state.errors.name[0]}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  defaultValue={template?.description || ""}
                  placeholder="Enter template description"
                  rows={3}
                />
                {state.errors?.description && (
                  <p className="text-sm text-destructive mt-1">{state.errors.description[0]}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Select name="category" defaultValue={template?.category}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {TEMPLATE_CATEGORIES.map(category => (
                        <SelectItem key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {state.errors?.category && (
                    <p className="text-sm text-destructive mt-1">{state.errors.category[0]}</p>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={isActive}
                    onCheckedChange={setIsActive}
                  />
                  <Label htmlFor="is_active">Active</Label>
                  <input type="hidden" name="is_active" value={isActive.toString()} />
                </div>
              </div>

              {/* Tags */}
              <div>
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X 
                        className="h-3 w-3 cursor-pointer" 
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add tag"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <input type="hidden" name="tags" value={JSON.stringify(tags)} />
              </div>
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Template Content *</CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant={previewMode === 'editor' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('editor')}
                  >
                    <Code className="h-4 w-4 mr-1" />
                    Editor
                  </Button>
                  <Button
                    type="button"
                    variant={previewMode === 'preview' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setPreviewMode('preview')}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Preview
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {previewMode === 'editor' ? (
                <RichTextEditor
                  value={content}
                  onChange={setContent}
                  placeholder="Enter your template content here. Use {{ token_name }} for dynamic content."
                />
              ) : (
                <div 
                  className="min-h-[300px] p-4 border rounded-md bg-background"
                  dangerouslySetInnerHTML={{ __html: content }}
                />
              )}
              <input type="hidden" name="content" value={content} />
              {state.errors?.content && (
                <p className="text-sm text-destructive mt-1">{state.errors.content[0]}</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Available Tokens */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Available Tokens</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue={availableTokens[0]?.name.toLowerCase().replace(/\s+/g, '-')}>
                <TabsList className="grid w-full grid-cols-2">
                  {availableTokens.slice(0, 2).map(group => (
                    <TabsTrigger 
                      key={group.name} 
                      value={group.name.toLowerCase().replace(/\s+/g, '-')}
                      className="text-xs"
                    >
                      {group.name.split(' ')[0]}
                    </TabsTrigger>
                  ))}
                </TabsList>
                
                {availableTokens.map(group => (
                  <TabsContent 
                    key={group.name} 
                    value={group.name.toLowerCase().replace(/\s+/g, '-')}
                    className="space-y-2"
                  >
                    <p className="text-sm text-muted-foreground mb-3">
                      {group.description}
                    </p>
                    {group.tokens.map(token => (
                      <div key={token.key} className="space-y-1">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="w-full justify-start text-left h-auto p-2"
                          onClick={() => insertToken(token.key)}
                        >
                          <div>
                            <div className="font-medium text-xs">{token.label}</div>
                            <div className="text-xs text-muted-foreground">
                              {`{{ ${token.key} }}`}
                            </div>
                          </div>
                        </Button>
                      </div>
                    ))}
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Button type="submit" className="w-full">
                  {mode === 'create' ? 'Create Template' : 'Update Template'}
                </Button>
                <Button type="button" variant="outline" className="w-full">
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </form>
  );
}
