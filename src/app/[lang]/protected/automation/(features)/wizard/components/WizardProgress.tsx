import { WizardConfig } from "../../../lib/types";
import { CheckIcon } from "lucide-react";

interface WizardProgressProps {
  wizardConfig: WizardConfig;
  currentStep: string;
  draftId: string;
  translatedLabels?: Record<string, string>;
}

/**
 * Wizard progress component
 * Displays the steps in the wizard with their completion status
 */
export default function WizardProgress({
  wizardConfig,
  currentStep,
  draftId: _draftId,
  translatedLabels,
}: WizardProgressProps) {
  const steps = [...wizardConfig.stepsMap.values()];
  const currentStepIndex = steps.findIndex((step) => step.id === currentStep);

  // Get translations for step labels if provided
  const getStepLabel = (stepId: string, defaultLabel: string) => {
    if (!translatedLabels) return defaultLabel;
    return translatedLabels[stepId] || defaultLabel;
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isActive = step.id === currentStep;

          return (
            <div key={index} className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  isCompleted
                    ? "bg-primary text-primary-foreground"
                    : isActive
                      ? "bg-primary/20 border-2 border-primary text-primary"
                      : "bg-muted text-muted-foreground"
                }`}
              >
                {isCompleted ? <CheckIcon className="h-5 w-5" /> : index + 1}
              </div>
              <span className="text-xs mt-2">{getStepLabel(step.id, step.label)}</span>
            </div>
          );
        })}
      </div>

      <div
        className="mt-2 grid"
        style={{ gridTemplateColumns: `repeat(${steps.length - 1}, 1fr)` }}
      >
        {steps.slice(0, -1).map((_, index) => (
          <div
            key={index}
            className={`h-1 rounded-full ${index < currentStepIndex ? "bg-primary" : "bg-muted"}`}
          />
        ))}
      </div>
    </div>
  );
}
