import { DraftService } from "../../../lib/services/DraftService";
import { getWizardConfig } from "../../../lib/config/wizardRegistry";
import { ContentLayout } from "@/components";
import { i18n } from "@/lib/i18n/services/I18nService";

interface WizardLayoutProps {
  params: Promise<{
    draftId: string;
    lang: string;
  }>;
  children: React.ReactNode;
}

/**
 * Wizard layout component
 * Provides the shell for the wizard with progress indicator and form wrapper
 */
export default async function WizardLayout({ params, children }: WizardLayoutProps) {
  const resolvedParams = await params;
  const { draftId, lang } = resolvedParams;
  const draftResult = await DraftService.getDraft(draftId);

  if (!draftResult.success) {
    throw new Error(`Failed to get draft: ${draftResult.message}`);
  }

  const draft = draftResult.data;

  if (!draft) {
    throw new Error("Draft data is null");
  }

  const currentStep = draft.current_step;

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);
  const requestDictionary = dictionary.automation["request-wizard"];

  // Determine current step index and if it's the last step
  const steps = [...wizardConfig.stepsMap.values()];
  const currentStepIndex = steps.findIndex((step) => step.id === currentStep);

  // Use translated title and description from dictionary
  const translatedTitle = requestDictionary.title;
  const translatedDescription = requestDictionary.description;

  return (
    <ContentLayout title={translatedTitle} description={translatedDescription}>
      {children}
    </ContentLayout>
  );
}
