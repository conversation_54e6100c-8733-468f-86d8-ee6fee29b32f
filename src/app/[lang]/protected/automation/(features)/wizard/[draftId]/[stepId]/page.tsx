import { DraftService } from "../../../../lib/services/DraftService";
import { getWizardConfig } from "../../../../lib/config/wizardRegistry";
import { notFound } from "next/navigation";

interface StepPageProps {
  params: Promise<{
    draftId: string;
    stepId: string;
    lang: string;
  }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

/**
 * Dynamic step page component
 * Renders the appropriate step content based on the step ID
 */
export default async function StepPage({ params, searchParams }: StepPageProps) {
  const resolvedParams = await params;
  const { draftId, stepId } = resolvedParams;

  // Get draft data
  const draftResult = await DraftService.getDraft(draftId);

  if (!draftResult.success || !draftResult.data) {
    notFound();
  }

  const draft = draftResult.data;

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);
  // Get step configuration
  const stepConfig = wizardConfig.stepsMap.get(stepId);

  if (!stepConfig) {
    throw new Error(`Step not found: ${stepId}`);
  }

  // Get the component for this step
  const StepContent = stepConfig.component;

  return (
    <div className="space-y-6">
      <StepContent draft={draft} searchParams={searchParams} />
    </div>
  );
}
