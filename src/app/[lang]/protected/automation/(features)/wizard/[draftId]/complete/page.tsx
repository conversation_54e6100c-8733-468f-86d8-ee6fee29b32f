import { Button } from "@/components/ui/button";
import { DraftService } from "../../../../lib/services/DraftService";
import { getWizardConfig } from "../../../../lib/config/wizardRegistry";
import Link from "next/link";
import { CheckCircle } from "lucide-react";

interface CompletePageProps {
  params: Promise<{
    draftId: string;
    lang: string;
  }>;
}

/**
 * Completion page for the wizard
 * Displayed when all steps have been completed
 */
export default async function CompletePage({ params }: CompletePageProps) {
  const resolvedParams = await params;
  const { draftId, lang } = resolvedParams;

  // Get draft data
  const draftResult = await DraftService.getDraft(draftId);

  if (!draftResult.success) {
    throw new Error(`Failed to get draft: ${draftResult.message}`);
  }

  const draft = draftResult.data;

  if (!draft) {
    throw new Error("Draft data is null");
  }

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);

  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <CheckCircle className="h-16 w-16 text-green-500 mb-4" />

      <h1 className="text-2xl font-bold mb-2">Completed!</h1>
      <p className="text-muted-foreground mb-8">
        You have successfully completed the {wizardConfig.title} wizard.
      </p>

      <div className="flex gap-4">
        <Button variant="outline" asChild>
          <Link href={`/${lang}/protected/automation`}>Back to Dashboard</Link>
        </Button>

        <Button asChild>
          <Link href={`/${lang}/protected/automation/drafts/${draftId}/view`}>View Details</Link>
        </Button>
      </div>
    </div>
  );
}
