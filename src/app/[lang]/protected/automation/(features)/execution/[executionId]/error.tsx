"use client";

import { Card, CardContent } from "@/components/ui/card";
import { XCircle } from "lucide-react";
import { H2, P } from "@/components/typography";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function ExecutionError({ error }: { error: Error }) {
  return (
    <Card className="max-w-2xl mx-auto">
      <CardContent className="pt-6 flex flex-col items-center text-center">
        <XCircle className="h-24 w-24 text-destructive mb-6" />
        <H2 className="mb-2">Error Loading Execution</H2>
        <P className="text-muted-foreground mb-8">{error.message}</P>
        <Button asChild>
          <Link href="/en/protected/automation">Back to Dashboard</Link>
        </Button>
      </CardContent>
    </Card>
  );
}
