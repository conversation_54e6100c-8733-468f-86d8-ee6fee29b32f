import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { H2 } from "@/components/typography";

export default function ExecutionLoading() {
  return (
    <Card className="max-w-2xl mx-auto">
      <CardContent className="pt-6 flex flex-col items-center text-center">
        <Loader2 className="h-24 w-24 text-primary animate-spin mb-6" />
        <H2 className="mb-2">Loading Execution Status</H2>
      </CardContent>
    </Card>
  );
}
