"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { H3, P } from "@/components/typography";
import { createEmployeeDraft } from "./actions/createEmployeeDraft";
import { ActionState } from "@/lib/types/responses/actionState";
import { useParams } from "next/navigation";
import { i18n } from "@/lib/i18n/services/I18nService";
import { UserPlus, ArrowRight } from "lucide-react";
import { motion } from "framer-motion";
import { Draft } from "../../lib/types/draft";

/**
 * Employee Wizard Entry Page
 * This page serves as the entry point for the employee creation wizard.
 * It provides information about the wizard and a button to start the process.
 */
export default function EmployeeWizardPage() {
  // Get the current language from the URL
  const { lang } = useParams();

  // Get the dictionary for translations
  const dictionary = i18n.getDictionary(lang as string);

  // Initial state for form actions
  const initialState: ActionState<Draft> = {
    success: true,
    error: "",
    data: null,
  };

  // Form action for creating a draft
  const [state, formAction] = useActionState(createEmployeeDraft, initialState);

  return (
    <div className="space-y-6">
      <div className="max-w-2xl mx-auto text-center space-y-6">
        {/* Animated icon */}
        <motion.div
          className="flex justify-center mb-4"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="bg-primary/10 p-6 rounded-full">
            <UserPlus className="h-16 w-16 text-primary" />
          </div>
        </motion.div>

        <H3>{dictionary.employee?.wizard?.createTitle || "Create a New Employee"}</H3>
        <P className="text-muted-foreground">
          {dictionary.employee?.wizard?.createDescription ||
            "This wizard will guide you through the process of creating a new employee record. You can save your progress at any time and come back later to complete it."}
        </P>

        {state.error && (
          <div className="bg-destructive/10 text-destructive p-3 rounded-md mb-4 max-w-md mx-auto">
            {state.error}
          </div>
        )}

        <div className="mt-8">
          <form action={formAction}>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Button type="submit" size="lg" className="px-6 py-6 text-lg gap-2 group">
                {dictionary.employee?.wizard?.startButton || "Start Employee Creation"}
                <motion.div
                  animate={{ x: [0, 5, 0] }}
                  transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
                >
                  <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </motion.div>
              </Button>
            </motion.div>
          </form>
        </div>
      </div>
    </div>
  );
}
