import { Database } from "@/lib/types/database.types";
import { Draft, DraftInsert, DraftUpdate } from "../../../lib/types";

/**
 * Employee types from database schema
 */
export type Employee = Database["public"]["Tables"]["employees"]["Row"];
export type EmployeeInsert = Database["public"]["Tables"]["employees"]["Insert"];
export type EmployeeUpdate = Database["public"]["Tables"]["employees"]["Update"];

/**
 * Employee draft types
 */
export type EmployeeDraft = Draft<Employee>;
export type EmployeeDraftInsert = DraftInsert<EmployeeInsert & { email: string; role: string }>;
export type EmployeeDraftUpdate = DraftUpdate<EmployeeUpdate>;
