"use client";
import { AvailabilityGrid, TimeSlot } from "@/components/ui/scheduling";
import { useState, useEffect } from "react";

interface FamilyAvailabilityGridProps {
  initialTimeSlots: TimeSlot[];
  selectedContactId: string;
  allAvailability: Record<string, TimeSlot[]>;
  onAvailabilityChange: (newAvailability: Record<string, TimeSlot[]>) => void;
}

export function FamilyAvailabilityGrid({
  initialTimeSlots,
  selectedContactId,
  allAvailability,
  onAvailabilityChange,
}: FamilyAvailabilityGridProps) {
  // Local state for time slots
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>(initialTimeSlots);

  // Update local state when props change
  useEffect(() => {
    setTimeSlots(initialTimeSlots);
  }, [initialTimeSlots, selectedContactId]);

  // Handle time slot toggle
  const handleTimeSlotToggle = (timeSlot: TimeSlot) => {
    // Find if this slot already exists
    const existingIndex = timeSlots.findIndex(
      (slot) => slot.day === timeSlot.day && slot.hour === timeSlot.hour
    );

    let newTimeSlots: TimeSlot[];

    if (existingIndex >= 0) {
      // Remove the slot if it exists
      newTimeSlots = [...timeSlots];
      newTimeSlots.splice(existingIndex, 1);
    } else {
      // Add new slot
      newTimeSlots = [...timeSlots, { ...timeSlot, available: true }];
    }

    // Update local state
    setTimeSlots(newTimeSlots);

    // Update parent component
    const newAvailability = { ...allAvailability };
    newAvailability[selectedContactId] = newTimeSlots;
    onAvailabilityChange(newAvailability);
  };

  return (
    <AvailabilityGrid
      timeSlots={timeSlots}
      onTimeSlotToggle={handleTimeSlotToggle}
      startHour={9}
      endHour={18}
      daysOfWeek={["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"]}
      showHeader={false}
      showFooter={false}
    />
  );
}
