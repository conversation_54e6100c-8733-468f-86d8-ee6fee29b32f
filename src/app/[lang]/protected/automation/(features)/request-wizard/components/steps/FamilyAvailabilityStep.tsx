"use server";

import { Draft } from "../../../../lib/types";
import { Button } from "@/components/ui/button";
import { User, Plus } from "lucide-react";
import Link from "next/link";
import React from "react";
import { FamilyAvailabilityClient } from "./FamilyAvailabilityClient";

interface FamilyAvailabilityStepProps {
  draft: Draft<any>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

/**
 * Family Availability Step for the Request Wizard
 * Server component that uses URL parameters for state
 */
export default async function FamilyAvailabilityStep({
  draft,
  searchParams,
}: FamilyAvailabilityStepProps) {
  // Get data from draft
  const data = draft.data || {};
  const relatedContacts = data.relatedContacts || [];

  // Get selected contact from URL parameter only
  const selectedContactId = ((await searchParams)?.contact as string) || null;

  // Parse availability data
  const contactAvailability = data.contact_availability
    ? JSON.parse(data.contact_availability)
    : {};

  // Create a dictionary object for translations
  const dictionary = {
    family_availability: {
      title: "Disponibilité de la famille",
      selectContact: "Sélectionnez un contact pour définir sa disponibilité",
      noContactsAdded: "Aucun contact ajouté pour l'instant.",
      addContacts: "Ajouter des contacts",
      weeklyAvailability: "Disponibilité hebdomadaire",
      clickToMark: "Cliquez sur les plages horaires pour les marquer comme disponibles",
      changesAutoSaved: "Les modifications seront enregistrées lorsque vous cliquerez sur Suivant",
      morning: "Matin",
      noon: "Midi",
      afternoon: "Après-midi",
      evening: "Soirée",
      selectToSet: "Sélectionnez un contact pour définir sa disponibilité",
    },
  };

  return (
    <div className="bg-black text-white p-6 rounded-lg">
      <div className="space-y-4">
        {/* This is a standard form that will be processed by the wizard */}

        <div className="flex flex-col md:flex-row gap-6">
          {/* Left column: Contact selection */}
          <div className="w-full md:w-1/4">
            <div className="mb-3">
              <h3 className="text-lg font-bold">
                {dictionary?.family_availability?.title || "Family Availability"}
              </h3>
              <p className="text-xs text-gray-400">
                {dictionary?.family_availability?.selectContact ||
                  "Select a contact to set their availability"}
              </p>
            </div>

            <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
              {relatedContacts.map((contact: any) => (
                <Link
                  key={contact.id}
                  href={`?contact=${contact.id}`}
                  className={`block p-3 rounded-md cursor-pointer flex items-center text-left ${
                    selectedContactId === contact.id
                      ? "bg-primary/20 border border-primary/50"
                      : "bg-gray-800 hover:bg-gray-700 border border-transparent"
                  }`}
                >
                  <User className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium">{contact.name}</p>
                    <p className="text-xs text-gray-400">{contact.relationshipType}</p>
                  </div>
                </Link>
              ))}

              {relatedContacts.length === 0 && (
                <div className="p-4 text-center bg-gray-800 rounded-md">
                  <p className="text-sm text-gray-400">
                    {dictionary?.family_availability?.noContactsAdded || "No contacts added yet."}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-3 bg-transparent border border-gray-700 hover:bg-gray-700 text-gray-300"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    {dictionary?.family_availability?.addContacts || "Add Contacts"}
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Right column: Availability grid (client component) */}
          <div className="w-full md:w-3/4">
            <FamilyAvailabilityClient
              draft={draft}
              selectedContactId={selectedContactId}
              initialAvailability={contactAvailability}
              relatedContacts={relatedContacts}
              dictionary={dictionary}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
