"use server";

import { Draft } from "../../../../lib/types";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { ContactSearchCombobox } from "@/app/[lang]/protected/contact/(features)/management/components/ContactSearchCombobox";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Trash } from "lucide-react";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";
import { addContact, removeContact } from "../../actions/manageContacts";

interface RelatedContactsStepProps {
  draft: Draft<any>;
}

/**
 * Related Contacts Step for the Request Wizard
 * Allows selecting contacts that are related to the request
 */
export default async function RelatedContactsStep({ draft }: RelatedContactsStepProps) {
  // Get data from draft
  const data = draft.data || {};
  const relatedContacts = data.relatedContacts || [];

  // Get dictionary for translations
  const dictionary = await getDomainFeatureDictionary("automation", "request-wizard");

  return (
    <div className="bg-black text-white p-6 rounded-lg">
      <div className="space-y-4">
        <div className="mb-2">
          <h3 className="text-lg font-bold">{dictionary.related_contacts.title}</h3>
          <p className="text-xs text-gray-400">{dictionary.related_contacts.description}</p>
        </div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Left column: Add new contact form */}
          <div className="w-full md:w-1/3">
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-4">
              <h4 className="text-sm font-medium border-b border-gray-800 pb-2 mb-3">
                {dictionary.related_contacts.addNewContact}
              </h4>

              <div className="space-y-4">
                <div>
                  <Label className="block text-gray-400 text-xs mb-1" htmlFor="contact_id">
                    {dictionary.related_contacts.contact} <span className="text-red-500">*</span>
                  </Label>
                  <div className="bg-gray-800 rounded-md">
                    <ContactSearchCombobox
                      label=""
                      placeholder={dictionary.related_contacts.searchContact}
                      emptyMessage={dictionary.related_contacts.noContactsFound}
                      minCharactersMessage={dictionary.related_contacts.minCharactersMessage}
                      name="contact_id"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Search for an existing contact</p>
                </div>

                <div>
                  <Label htmlFor="relationship_type" className="block text-gray-400 text-xs mb-1">
                    {dictionary.related_contacts.relationship}{" "}
                    <span className="text-red-500">*</span>
                  </Label>
                  <div className="bg-gray-800 rounded-md">
                    <Select name="relationship_type" defaultValue="family">
                      <SelectTrigger
                        id="relationship_type"
                        className="bg-transparent border-0 focus:ring-0"
                      >
                        <SelectValue placeholder={dictionary.related_contacts.selectRelationship} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="family">
                          {dictionary.related_contacts.familyMember}
                        </SelectItem>
                        <SelectItem value="guardian">
                          {dictionary.related_contacts.guardian}
                        </SelectItem>
                        <SelectItem value="caregiver">
                          {dictionary.related_contacts.caregiver}
                        </SelectItem>
                        <SelectItem value="professional">
                          {dictionary.related_contacts.professional}
                        </SelectItem>
                        <SelectItem value="other">{dictionary.related_contacts.other}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Specify the relationship to the client
                  </p>
                </div>

                <Button
                  type="submit"
                  variant="outline"
                  size="sm"
                  value="true"
                  className="bg-primary/20 border border-primary/50 hover:bg-primary/30 text-primary-foreground w-full mt-2"
                  formAction={addContact.bind(null, draft.id)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {dictionary.related_contacts.addContact}
                </Button>
              </div>
            </div>
          </div>

          {/* Right column: Existing contacts list */}
          <div className="w-full md:w-2/3">
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-4 h-full">
              <h4 className="text-sm font-medium border-b border-gray-800 pb-2 mb-3">
                {dictionary.related_contacts.selectedContacts}
              </h4>

              {relatedContacts.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-h-[300px] overflow-y-auto pr-1">
                  {relatedContacts.map((contact: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-800 rounded-md border border-gray-700"
                    >
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-white text-sm truncate">{contact.name}</p>
                        <p className="text-xs text-gray-400 truncate">{contact.relationshipType}</p>
                      </div>
                      <input type="hidden" name="remove_contact" value={index.toString()} />
                      <Button
                        type="submit"
                        variant="outline"
                        size="sm"
                        value="true"
                        className="bg-transparent border-0 hover:bg-gray-700 text-gray-300 h-7 w-7 p-0 ml-2 flex-shrink-0"
                        formAction={removeContact.bind(null, draft.id)}
                        title={dictionary.related_contacts.remove}
                      >
                        <Trash className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-[200px] text-center">
                  <p className="text-gray-400 text-sm mb-2">No contacts added yet</p>
                  <p className="text-xs text-gray-500">
                    Use the form on the left to add contacts to this request
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
