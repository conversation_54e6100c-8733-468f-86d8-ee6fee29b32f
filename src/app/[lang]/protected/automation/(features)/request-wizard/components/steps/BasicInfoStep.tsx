"use server";

import { Draft } from "../../../../lib/types";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";

interface BasicInfoStepProps {
  draft: Draft<any>;
}

/**
 * Basic Info Step for the Request Wizard
 * Collects basic information about the request
 */
export default async function BasicInfoStep({ draft }: BasicInfoStepProps) {
  // Get data from draft
  const data = draft.data || {};

  // Get dictionary for translations
  const dictionary = await getDomainFeatureDictionary("automation", "request-wizard");

  // Fetch locations and services from the database
  const locations = await ProfileService.getLocations();
  const services = await ProfileService.getServices();

  return (
    <div className="bg-black text-white p-6 rounded-lg">
      <div className="space-y-6">
        {/* Title and description fields are hidden but still in the DOM with type="hidden" */}
        <input
          type="hidden"
          id="title"
          name="title"
          defaultValue={data.title || "Untitled Request"}
        />

        <input
          type="hidden"
          id="description"
          name="description"
          defaultValue={data.description || "No description provided"}
        />

        {/* Service Selection */}
        <div>
          <Label htmlFor="service_id" className="block text-gray-400 text-sm mb-2">
            {dictionary.basic_info.service} <span className="text-red-500">*</span>
          </Label>
          <div className="bg-gray-800 rounded-md">
            <Select name="service_id" defaultValue={data?.service_id}>
              <SelectTrigger id="service_id" className="bg-transparent border-0 focus:ring-0">
                <SelectValue placeholder={dictionary.basic_info.selectService} />
              </SelectTrigger>
              <SelectContent>
                {services.length > 0 &&
                  services.map((service) => (
                    <SelectItem key={service.id} value={service.id}>
                      {service.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
          <p className="text-xs text-gray-500 mt-1">Select the type of service needed</p>
        </div>

        {/* Location Selection */}
        <div>
          <Label htmlFor="location_id" className="block text-gray-400 text-sm mb-2">
            {dictionary.basic_info.location} <span className="text-red-500">*</span>
          </Label>
          <div className="bg-gray-800 rounded-md">
            <Select name="location_id" defaultValue={data?.location_id}>
              <SelectTrigger id="location_id" className="bg-transparent border-0 focus:ring-0">
                <SelectValue placeholder={dictionary.basic_info.selectLocation} />
              </SelectTrigger>
              <SelectContent>
                {locations.length > 0 &&
                  locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
          <p className="text-xs text-gray-500 mt-1">Select where the service will be provided</p>
        </div>

        {/* Priority field is hidden but still in the DOM with type="hidden" */}
        <input
          type="hidden"
          id="priority"
          name="priority"
          defaultValue={data.priority || "medium"}
        />
      </div>
    </div>
  );
}
