"use server";

import { Draft } from "../../../../lib/types";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { DatePicker } from "@/components/ui/date-picker";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";

interface ServiceRequirementsStepProps {
  draft: Draft<any>;
}

/**
 * Service Requirements Step for the Request Wizard
 * Collects information about service requirements
 */
export default async function ServiceRequirementsStep({ draft }: ServiceRequirementsStepProps) {
  // Get data from draft
  const data = draft.data || {};
  const serviceRequirements = data.serviceRequirements || {};

  // Get dictionary for translations
  const dictionary = await getDomainFeatureDictionary("automation", "request-wizard");

  return (
    <div className="bg-black text-white p-6 rounded-lg">
      <div className="space-y-6">
        {/* Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="start_date" className="block text-gray-400 text-sm mb-2">
              {dictionary.service_requirements.startDate} <span className="text-red-500">*</span>
            </Label>
            <div className="bg-gray-800 rounded-md">
              <DatePicker
                name="start_date"
                defaultValue={data.start_date}
                placeholder={dictionary.service_requirements.selectStartDate}
                className="bg-transparent border-0 focus:ring-0 w-full"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">Required start date for the service</p>
          </div>

          <div>
            <Label htmlFor="end_date" className="block text-gray-400 text-sm mb-2">
              {dictionary.service_requirements.endDate}
            </Label>
            <div className="bg-gray-800 rounded-md">
              <DatePicker
                name="end_date"
                defaultValue={data.end_date}
                placeholder={dictionary.service_requirements.selectEndDate}
                className="bg-transparent border-0 focus:ring-0 w-full"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">Optional end date for the service</p>
          </div>
        </div>

        {/* Periodicity */}
        <div>
          <Label htmlFor="periodicity" className="block text-gray-400 text-sm mb-2">
            {dictionary.service_requirements.periodicity || "Periodicity"}{" "}
            <span className="text-red-500">*</span>
          </Label>
          <div className="bg-gray-800 rounded-md">
            <Select name="periodicity" defaultValue={serviceRequirements.periodicity || "weekly"}>
              <SelectTrigger id="periodicity" className="bg-transparent border-0 focus:ring-0">
                <SelectValue
                  placeholder={
                    dictionary.service_requirements.selectPeriodicity || "Select periodicity"
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">
                  {dictionary.service_requirements.daily || "Daily"}
                </SelectItem>
                <SelectItem value="weekly">
                  {dictionary.service_requirements.weekly || "Weekly"}
                </SelectItem>
                <SelectItem value="monthly">
                  {dictionary.service_requirements.monthly || "Monthly"}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <p className="text-xs text-gray-500 mt-1">How often the service should occur</p>
        </div>

        {/* Frequency Count */}
        <div>
          <Label htmlFor="frequency_count" className="block text-gray-400 text-sm mb-2">
            {dictionary.service_requirements.frequencyCount || "Frequency"}{" "}
            <span className="text-red-500">*</span>
          </Label>
          <div className="bg-gray-800 rounded-md">
            <Input
              id="frequency_count"
              name="frequency_count"
              type="number"
              min="1"
              max="30"
              defaultValue={serviceRequirements.frequency_count || "1"}
              placeholder={
                dictionary.service_requirements.enterFrequencyCount ||
                "Enter number of times per period"
              }
              required
              className="bg-transparent border-0 focus:ring-0"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {dictionary.service_requirements.timesPerPeriod || "Times per period"}
          </p>
        </div>

        {/* Duration */}
        <div>
          <Label htmlFor="duration" className="block text-gray-400 text-sm mb-2">
            {dictionary.service_requirements.duration || "Duration (minutes)"}{" "}
            <span className="text-red-500">*</span>
          </Label>
          <div className="bg-gray-800 rounded-md">
            <Input
              id="duration"
              name="duration"
              type="number"
              min="1"
              max="480"
              defaultValue={serviceRequirements.duration || "60"}
              placeholder={
                dictionary.service_requirements.enterDuration || "Enter duration in minutes"
              }
              required
              className="bg-transparent border-0 focus:ring-0"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Duration of each service session in minutes</p>
        </div>
      </div>
    </div>
  );
}
