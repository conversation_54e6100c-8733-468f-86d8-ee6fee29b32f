/**
 * Utilities for processing form data
 */

/**
 * Process form data to extract values into a structured object
 * @param formData The form data to process
 * @returns Processed form data as an object
 */
export function transformFormDataToObject(formData: FormData): Record<string, any> {
  const result: Record<string, any> = {};

  // Process regular fields
  for (const [key, value] of formData.entries()) {
    // Skip special fields
    if (key.endsWith("_indices")) continue;

    // Handle array fields
    if (key.includes("[") && key.includes("]")) {
      const match = key.match(/^([^\[]+)\[([^\]]+)\]$/);
      if (match) {
        const [_, arrayName, index] = match;

        if (!result[arrayName]) {
          result[arrayName] = [];
        }

        // Handle numeric indices
        if (!isNaN(Number(index))) {
          const numIndex = Number(index);
          result[arrayName][numIndex] = value;
        }
        // Handle object properties
        else {
          if (!result[arrayName][0]) {
            result[arrayName][0] = {};
          }
          result[arrayName][0][index] = value;
        }
      }
    }
    // Regular fields
    else {
      result[key] = value;
    }
  }

  // Clean up empty array entries
  for (const key in result) {
    if (Array.isArray(result[key])) {
      result[key] = result[key].filter((item) => item !== undefined && item !== "");
    }
  }

  return result;
}

/**
 * Check if the form was submitted (final step)
 * @param formData The form data to check
 * @returns True if the form was submitted
 */
export function isFormSubmitted(formData: FormData): boolean {
  return formData.get("submitted") === "true";
}
