"use server";

import { DraftService } from "../services/DraftService";
import { logger } from "@/lib/logger/services/LoggerService";
import { transformFormDataToObject } from "./transformFormDataToObject";
import { Json } from "@/lib/types/database.types";

/**
 * Process form data and get the current draft
 * @param draftId The draft ID
 * @param formData The form data
 * @returns The draft and processed form data
 */
export async function dtoDraftData(draftId: string, formData: FormData) {
  try {
    // Get current draft
    const draftResult = await DraftService.getDraft(draftId);

    if (!draftResult.success) {
      logger.error(`Error getting draft: ${draftResult.error}`);
      throw new Error(`Failed to get draft: ${draftResult.message}`);
    }

    const draft = draftResult.data;
    if (!draft) {
      throw new Error("Draft data is null");
    }

    // Process form data
    const formDataObj = transformFormDataToObject(formData);

    // Get current draft data and merge with new data
    const currentData = (draft.data as Json) || ({} as J<PERSON>);
    const updatedData = {
      ...(currentData as object),
      ...formDataObj,
    } as Json;

    return { draft, updatedData };
  } catch (error) {
    logger.error(`Error processing draft data: ${error}`);
    throw new Error(`Failed to process draft data: ${error}`);
  }
}
