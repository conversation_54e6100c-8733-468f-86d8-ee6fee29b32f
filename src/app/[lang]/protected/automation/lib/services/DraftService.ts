import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  ServiceResponse,
  errorResponse,
  successResponse,
} from "@/lib/types/responses/serviceResponse";
import { Draft, DraftInsert, DraftUpdate, DraftFilters } from "../types";
import { Database, Json } from "@/lib/types/database.types";

/**
 * Service for managing drafts
 * Uses generics to provide type safety for different draft types
 */
export class DraftService {
  /**
   * Get all drafts for the current user and organization
   * @param filters Optional filters for listing drafts
   * @returns Service response with the drafts
   */
  static async listDrafts<TData = Json>(
    filters?: DraftFilters
  ): Promise<ServiceResponse<Draft<TData>[]>> {
    try {
      const supabase = await createClient();

      let query = supabase.from("drafts").select("*");

      // Apply filters
      if (filters?.userId) {
        query = query.eq("user_id", filters.userId);
      }

      if (filters?.organizationId) {
        query = query.eq("organization_id", filters.organizationId);
      }

      if (filters?.workflowType) {
        query = query.eq("workflow_type", filters.workflowType);
      }

      // Order by updated_at
      query = query.order("updated_at", { ascending: false });

      const { data, error } = await query;

      if (error) {
        logger.error(`Error fetching drafts: ${error.message}`);
        return errorResponse(error, `Failed to fetch drafts: ${error.message}`);
      }

      return successResponse(data as Draft<TData>[], "Successfully fetched drafts");
    } catch (error) {
      logger.error(`Unexpected error fetching drafts: ${error}`);
      return errorResponse(error, `Unexpected error fetching drafts`);
    }
  }

  /**
   * Get a draft by ID
   * @param id The draft ID
   * @returns Service response with the draft
   */
  static async getDraft<TData = Json>(id: string): Promise<ServiceResponse<Draft<TData>>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("drafts").select("*").eq("id", id).single();

      if (error) {
        logger.error(`Error fetching draft: ${error.message}`);
        return errorResponse(error, `Failed to fetch draft: ${error.message}`);
      }

      return successResponse(data as Draft<TData>, "Successfully fetched draft");
    } catch (error) {
      logger.error(`Unexpected error fetching draft: ${error}`);
      return errorResponse(error, `Unexpected error fetching draft`);
    }
  }

  /**
   * Create a new draft
   * @param draft The draft to create
   * @returns Service response with the created draft
   */
  static async createDraft<TData = Json>(
    draft: DraftInsert<TData>
  ): Promise<ServiceResponse<Draft<TData>>> {
    try {
      const supabase = await createClient();

      // Convert generic draft to database-compatible format
      const dbDraft = {
        user_id: draft.user_id,
        organization_id: draft.organization_id,
        workflow_type: draft.workflow_type,
        current_step: draft.current_step,
        data: draft.data as Json,
      };

      const { data, error } = await supabase.from("drafts").insert(dbDraft).select().single();

      if (error) {
        logger.error(`Error creating draft: ${error.message}`);
        return errorResponse(error, `Failed to create draft: ${error.message}`);
      }

      return successResponse(data as Draft<TData>, "Successfully created draft");
    } catch (error) {
      logger.error(`Unexpected error creating draft: ${error}`);
      return errorResponse(error, `Unexpected error creating draft`);
    }
  }

  /**
   * Update an existing draft
   * @param id The draft ID
   * @param draft The draft updates
   * @returns Service response with the updated draft
   */
  static async updateDraft<TData = Json>(
    id: string,
    draft: DraftUpdate<TData>
  ): Promise<ServiceResponse<Draft<TData>>> {
    try {
      const supabase = await createClient();

      // Convert generic draft update to database-compatible format
      const dbDraft: Database["public"]["Tables"]["drafts"]["Update"] = {};

      if (draft.current_step !== undefined) {
        dbDraft.current_step = draft.current_step;
      }

      if (draft.data !== undefined) {
        dbDraft.data = draft.data as Json;
      }

      const { data, error } = await supabase
        .from("drafts")
        .update(dbDraft)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating draft: ${error.message}`);
        return errorResponse(error, `Failed to update draft: ${error.message}`);
      }

      return successResponse(data as Draft<TData>, "Successfully updated draft");
    } catch (error) {
      logger.error(`Unexpected error updating draft: ${error}`);
      return errorResponse(error, `Unexpected error updating draft`);
    }
  }

  /**
   * Delete a draft
   * @param id The draft ID
   * @returns Service response with success status
   */
  static async deleteDraft(id: string): Promise<ServiceResponse<null>> {
    try {
      const supabase = await createClient();

      const { error } = await supabase.from("drafts").delete().eq("id", id);

      if (error) {
        logger.error(`Error deleting draft: ${error.message}`);
        return errorResponse(error, `Failed to delete draft: ${error.message}`);
      }

      return successResponse(null, "Successfully deleted draft");
    } catch (error) {
      logger.error(`Unexpected error deleting draft: ${error}`);
      return errorResponse(error, `Unexpected error deleting draft`);
    }
  }
}
