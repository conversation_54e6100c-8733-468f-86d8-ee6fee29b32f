"use server";

import { redirect } from "next/navigation";
import { dtoDraftData } from "../../lib/dto/dtoDraftData";
import { updateDraft } from "../drafts/update";
import { getWizardConfig } from "../../lib/config/wizardRegistry";
import { getCurrentLanguage, buildLanguagePath } from "./navigationUtils";

/**
 * Navigate to the next step in the wizard
 * @param draftId The draft ID
 * @param formData The form data
 */
export async function nextStep(draftId: string, formData: FormData) {
  let nextStepId = "";

  // Process draft data
  const { draft, updatedData } = await dtoDraftData(draftId, formData);

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);
  const currentStep = wizardConfig.stepsMap.get(draft.current_step);

  if (!currentStep) {
    throw new Error(`Step not found in configuration: ${draft.current_step}`);
  }

  // Get next step ID from the configuration
  nextStepId = currentStep.nextStepId || draft.current_step;

  // Update draft
  await updateDraft(draftId, updatedData, nextStepId);

  // Get current language
  const lang = await getCurrentLanguage();

  // Redirect to next step
  redirect(await buildLanguagePath(`/protected/automation/wizard/${draftId}/${nextStepId}`, lang));
}
