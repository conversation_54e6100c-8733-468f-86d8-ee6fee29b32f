"use server";

import { redirect } from "next/navigation";
import { dtoDraftData } from "../../lib/dto/dtoDraftData";
import { updateDraft } from "../drafts/update";
import { getWizardConfig } from "../../lib/config/wizardRegistry";
import { getCurrentLanguage, buildLanguagePath } from "./navigationUtils";

/**
 * Navigate to the previous step in the wizard
 * @param draftId The draft ID
 * @param formData The form data
 */
export async function previousStep(draftId: string, formData: FormData) {
  let previousStepId = "";

  // Process draft data
  const { draft, updatedData } = await dtoDraftData(draftId, formData);

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);
  const currentStep = wizardConfig.stepsMap.get(draft.current_step);

  if (!currentStep) {
    throw new Error(`Step not found in configuration: ${draft.current_step}`);
  }

  // Get previous step ID from the configuration
  previousStepId = currentStep.previousStepId || draft.current_step;

  // Update draft
  await updateDraft(draftId, updatedData, previousStepId);

  // Get current language
  const lang = await getCurrentLanguage();

  // Redirect to previous step
  redirect(
    await buildLanguagePath(`/protected/automation/wizard/${draftId}/${previousStepId}`, lang)
  );
}
