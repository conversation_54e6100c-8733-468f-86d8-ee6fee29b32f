"use server";

import { revalidatePath } from "next/cache";
import { DraftService } from "../../lib/services/DraftService";
import { logger } from "@/lib/logger/services/LoggerService";
import { <PERSON><PERSON> } from "@/lib/types/database.types";

/**
 * Update draft with new data and step
 * @param draftId The draft ID
 * @param updatedData The updated data
 * @param nextStepId The next step ID
 */
export async function updateDraft(draftId: string, updatedData: Json, nextStepId: string) {
  try {
    const result = await DraftService.updateDraft(draftId, {
      current_step: nextStepId,
      data: updatedData,
    });

    if (!result.success) {
      logger.error(`Error updating draft: ${result.error}`);
      throw new Error(`Failed to update draft: ${result.message}`);
    }

    // Revalidate path
    revalidatePath(`/en/protected/automation/wizard/${draftId}`);

    return result.data;
  } catch (error) {
    logger.error(`Error updating draft: ${error}`);
    throw new Error(`Failed to update draft: ${error}`);
  }
}
