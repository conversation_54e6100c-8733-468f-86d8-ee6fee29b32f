"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  ActionState,
  errorActionState,
  successActionState,
} from "@/lib/types/responses/actionState";
import { DraftService } from "../../lib/services/DraftService";
import { Draft, DraftInsert } from "../../lib/types";
import { Json } from "@/lib/types/database.types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getWizardConfig } from "../../lib/config/wizardRegistry";

/**
 * Create a new draft for a specific workflow type
 * @param workflowType The workflow type
 * @param initialData Optional initial data for the draft
 * @returns The created draft
 */
export async function createDraft<TData = Json>(
  workflowType: string,
  initialData?: TData
): Promise<ActionState<Draft<TData>>> {
  try {
    // Get current user and organization
    const user = await auth.getCurrentUser();
    if (!user) {
      return errorActionState("User not authenticated");
    }

    const userId = user.id;
    const organizationId = await auth.getCurrentUserOrganizationId();

    if (!organizationId) {
      return errorActionState("Organization not found");
    }

    // Get wizard configuration to determine initial step
    const wizardConfig = getWizardConfig(workflowType);
    const initialStep = wizardConfig.initialStep;

    // Create draft object
    const draft: DraftInsert<TData> = {
      user_id: userId,
      organization_id: organizationId,
      workflow_type: workflowType,
      current_step: initialStep,
      data: initialData || ({} as TData),
    };

    // Create draft
    const result = await DraftService.createDraft<TData>(draft);

    if (!result.success) {
      logger.error(`Error creating draft: ${result.error}`);
      return errorActionState(`Failed to create draft: ${result.message}`);
    }

    // Revalidate path
    revalidatePath("/en/protected/automation");

    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error creating draft: ${error}`);
    return errorActionState("An unexpected error occurred while creating the draft");
  }
}

/**
 * Create a new draft and redirect to the first step
 * @param workflowType The workflow type
 * @param initialData Optional initial data for the draft
 */
export async function createDraftAndRedirect<TData = Json>(
  workflowType: string,
  initialData?: TData
): Promise<void> {
  const result = await createDraft<TData>(workflowType, initialData);

  if (!result.success) {
    throw new Error(result.error);
  }

  if (!result.data) {
    throw new Error("Draft data is null");
  }

  const draftId = result.data.id;
  const wizardConfig = getWizardConfig(workflowType);
  const initialStep = wizardConfig.initialStep;

  redirect(`/[lang]/protected/automation/wizard/${draftId}/${initialStep}`);
}
