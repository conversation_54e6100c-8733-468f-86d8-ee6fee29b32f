import { createClient } from "@/lib/supabase/server";
import {
  ServiceResponse,
  successResponse,
  errorResponse,
} from "@/lib/types/responses/serviceResponse";
import {
  Appointment,
  AppointmentInsert,
  AppointmentUpdate,
  AppointmentWithDetails,
  AppointmentFilters,
  AppointmentListResponse,
  AppointmentConflict,
  AppointmentStatus,
} from "../types";
import { APPOINTMENT_CONFIG } from "../config";

/**
 * Self-contained Appointment Service
 * Handles all appointment CRUD operations without external dependencies
 */
export class AppointmentService {
  private async getSupabase() {
    return await createClient();
  }

  /**
   * Create a new appointment
   */
  async createAppointment(
    appointmentData: AppointmentInsert
  ): Promise<ServiceResponse<Appointment>> {
    try {
      const supabase = await this.getSupabase();

      // Calculate duration in minutes
      const startTime = new Date(`2000-01-01T${appointmentData.start_time}`);
      const endTime = new Date(`2000-01-01T${appointmentData.end_time}`);
      const durationMinutes = Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));

      // Check for time conflicts
      const conflictCheck = await this.checkTimeConflicts(
        appointmentData.appointment_date,
        appointmentData.start_time,
        appointmentData.end_time,
        appointmentData.organization_id
      );

      if (!conflictCheck.success) {
        return errorResponse(conflictCheck.error, conflictCheck.message);
      }

      if (conflictCheck.data.hasConflict) {
        return errorResponse(
          "Time conflict detected",
          `There is already an appointment scheduled during this time: ${conflictCheck.data.message}`
        );
      }

      // Create the appointment
      const { data, error } = await supabase
        .from("appointments")
        .insert({
          ...appointmentData,
          duration_minutes: durationMinutes,
          // Set foreign keys to null for self-contained operation
          case_file_id: null,
          service_id: null,
          family_availability: null,
        })
        .select()
        .single();

      if (error) {
        return errorResponse(error, `Failed to create appointment: ${error.message}`);
      }

      return successResponse(data, "Appointment created successfully");
    } catch (error) {
      return errorResponse(error, "Appointment creation failed");
    }
  }

  /**
   * Get appointment by ID
   */
  async getAppointmentById(id: string): Promise<ServiceResponse<AppointmentWithDetails>> {
    try {
      const supabase = await this.getSupabase();

      const { data, error } = await supabase
        .from("appointments")
        .select("*")
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return errorResponse("Appointment not found", "The requested appointment does not exist");
        }
        return errorResponse(error, `Failed to fetch appointment: ${error.message}`);
      }

      // Add computed fields
      const appointmentWithDetails: AppointmentWithDetails = {
        ...data,
        duration_display: `${data.duration_minutes} minutes`,
        date_display: new Date(data.appointment_date).toLocaleDateString(),
        time_display: `${data.start_time} - ${data.end_time}`,
        status_display: APPOINTMENT_CONFIG.statuses[data.status]?.label || data.status,
      };

      return successResponse(appointmentWithDetails, "Appointment fetched successfully");
    } catch (error) {
      return errorResponse(error, "Failed to fetch appointment");
    }
  }

  /**
   * Update an existing appointment
   */
  async updateAppointment(
    id: string,
    updateData: AppointmentUpdate
  ): Promise<ServiceResponse<Appointment>> {
    try {
      const supabase = await this.getSupabase();

      // If time is being updated, calculate new duration and check conflicts
      let processedUpdateData = { ...updateData };

      if (updateData.start_time || updateData.end_time || updateData.appointment_date) {
        // Get current appointment data to fill in missing time fields
        const currentResult = await this.getAppointmentById(id);
        if (!currentResult.success) {
          return errorResponse(currentResult.error, currentResult.message);
        }

        const current = currentResult.data;
        const startTime = updateData.start_time || current.start_time;
        const endTime = updateData.end_time || current.end_time;
        const appointmentDate = updateData.appointment_date || current.appointment_date;

        // Calculate new duration
        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        const durationMinutes = Math.round((end.getTime() - start.getTime()) / (1000 * 60));

        // Check for conflicts (excluding current appointment)
        const conflictCheck = await this.checkTimeConflicts(
          appointmentDate,
          startTime,
          endTime,
          current.organization_id,
          id // Exclude current appointment from conflict check
        );

        if (!conflictCheck.success) {
          return errorResponse(conflictCheck.error, conflictCheck.message);
        }

        if (conflictCheck.data.hasConflict) {
          return errorResponse(
            "Time conflict detected",
            `There is already an appointment scheduled during this time: ${conflictCheck.data.message}`
          );
        }

        processedUpdateData.duration_minutes = durationMinutes;
      }

      const { data, error } = await supabase
        .from("appointments")
        .update(processedUpdateData)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        return errorResponse(error, `Failed to update appointment: ${error.message}`);
      }

      return successResponse(data, "Appointment updated successfully");
    } catch (error) {
      return errorResponse(error, "Appointment update failed");
    }
  }

  /**
   * Delete an appointment
   */
  async deleteAppointment(id: string): Promise<ServiceResponse<void>> {
    try {
      const supabase = await this.getSupabase();

      const { error } = await supabase
        .from("appointments")
        .delete()
        .eq("id", id);

      if (error) {
        return errorResponse(error, `Failed to delete appointment: ${error.message}`);
      }

      return successResponse(undefined, "Appointment deleted successfully");
    } catch (error) {
      return errorResponse(error, "Appointment deletion failed");
    }
  }

  /**
   * List appointments with filtering and pagination
   */
  async listAppointments(
    organizationId: string,
    filters: AppointmentFilters = {}
  ): Promise<ServiceResponse<AppointmentListResponse>> {
    try {
      const supabase = await this.getSupabase();

      const {
        status,
        date,
        search,
        page = 1,
        limit = APPOINTMENT_CONFIG.pagination.defaultLimit,
      } = filters;

      let query = supabase
        .from("appointments")
        .select("*", { count: "exact" })
        .eq("organization_id", organizationId)
        .order("appointment_date", { ascending: true })
        .order("start_time", { ascending: true });

      // Apply filters
      if (status) {
        query = query.eq("status", status);
      }

      if (date) {
        query = query.eq("appointment_date", date);
      }

      if (search) {
        query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        return errorResponse(error, `Failed to fetch appointments: ${error.message}`);
      }

      // Add computed fields to each appointment
      const appointmentsWithDetails: AppointmentWithDetails[] = (data || []).map(appointment => ({
        ...appointment,
        duration_display: `${appointment.duration_minutes} minutes`,
        date_display: new Date(appointment.appointment_date).toLocaleDateString(),
        time_display: `${appointment.start_time} - ${appointment.end_time}`,
        status_display: APPOINTMENT_CONFIG.statuses[appointment.status]?.label || appointment.status,
      }));

      const totalPages = Math.ceil((count || 0) / limit);

      const response: AppointmentListResponse = {
        appointments: appointmentsWithDetails,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
        },
      };

      return successResponse(response, "Appointments fetched successfully");
    } catch (error) {
      return errorResponse(error, "Failed to fetch appointments");
    }
  }

  /**
   * Check for time conflicts with existing appointments
   */
  async checkTimeConflicts(
    appointmentDate: string,
    startTime: string,
    endTime: string,
    organizationId: string,
    excludeAppointmentId?: string
  ): Promise<ServiceResponse<AppointmentConflict>> {
    try {
      const supabase = await this.getSupabase();

      let query = supabase
        .from("appointments")
        .select("*")
        .eq("organization_id", organizationId)
        .eq("appointment_date", appointmentDate)
        .neq("status", "cancelled") // Don't check against cancelled appointments
        .or(`and(start_time.lte.${startTime},end_time.gt.${startTime}),and(start_time.lt.${endTime},end_time.gte.${endTime}),and(start_time.gte.${startTime},end_time.lte.${endTime})`);

      // Exclude current appointment if updating
      if (excludeAppointmentId) {
        query = query.neq("id", excludeAppointmentId);
      }

      const { data, error } = await query;

      if (error) {
        return errorResponse(error, `Failed to check conflicts: ${error.message}`);
      }

      const hasConflict = (data || []).length > 0;
      const conflictMessage = hasConflict
        ? `Conflicts with: ${data?.map(apt => `${apt.title} (${apt.start_time}-${apt.end_time})`).join(", ")}`
        : undefined;

      const conflict: AppointmentConflict = {
        hasConflict,
        conflictingAppointments: data || [],
        message: conflictMessage,
      };

      return successResponse(conflict, "Conflict check completed");
    } catch (error) {
      return errorResponse(error, "Conflict check failed");
    }
  }

  /**
   * Update appointment status
   */
  async updateAppointmentStatus(
    id: string,
    status: AppointmentStatus
  ): Promise<ServiceResponse<Appointment>> {
    try {
      // Get current appointment to validate status transition
      const currentResult = await this.getAppointmentById(id);
      if (!currentResult.success) {
        return errorResponse(currentResult.error, currentResult.message);
      }

      const currentAppointment = currentResult.data;

      // Check if status transition is allowed
      const allowedTransitions = APPOINTMENT_CONFIG.statusTransitions[currentAppointment.status];
      if (!allowedTransitions.includes(status)) {
        return errorResponse(
          "Invalid status transition",
          `Cannot change status from ${currentAppointment.status} to ${status}`
        );
      }

      // Update with timestamp fields based on status
      const updateData: AppointmentUpdate = { status };

      if (status === "confirmed") {
        updateData.confirmed_at = new Date().toISOString();
      } else if (status === "completed") {
        updateData.completed_at = new Date().toISOString();
      }

      return await this.updateAppointment(id, updateData);
    } catch (error) {
      return errorResponse(error, "Status update failed");
    }
  }
}
