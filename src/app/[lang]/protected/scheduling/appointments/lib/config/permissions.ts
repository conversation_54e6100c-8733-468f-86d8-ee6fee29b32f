/**
 * Appointment management permissions
 */
export const APPOINTMENT_PERMISSIONS = {
  // Core CRUD permissions
  CREATE: "appointments:create",
  READ: "appointments:read", 
  UPDATE: "appointments:update",
  DELETE: "appointments:delete",
  
  // List and search permissions
  LIST: "appointments:list",
  SEARCH: "appointments:search",
  
  // Status management permissions
  CONFIRM: "appointments:confirm",
  COMPLETE: "appointments:complete", 
  CA<PERSON><PERSON>: "appointments:cancel",
  POSTPONE: "appointments:postpone",
  
  // Calendar permissions
  CALENDAR_VIEW: "appointments:calendar:view",
  
  // Administrative permissions
  ADMIN: "appointments:admin",
  
  // Organization-level permissions
  ORG_VIEW_ALL: "appointments:organization:view_all",
  OR<PERSON>_MANAGE_ALL: "appointments:organization:manage_all",
} as const;

/**
 * Permission groups for easier management
 */
export const APPOINTMENT_PERMISSION_GROUPS = {
  // Basic user permissions
  USER: [
    APPOINTMENT_PERMISSIONS.READ,
    APPOINTMENT_PERMISSIONS.LIST,
    APPOINTMENT_PERMISSIONS.CALENDAR_VIEW,
  ],
  
  // Staff permissions (can manage their own appointments)
  STAFF: [
    APPOINTMENT_PERMISSIONS.CREATE,
    APPOINTMENT_PERMISSIONS.READ,
    APPOINTMENT_PERMISSIONS.UPDATE,
    APPOINTMENT_PERMISSIONS.LIST,
    APPOINTMENT_PERMISSIONS.SEARCH,
    APPOINTMENT_PERMISSIONS.CONFIRM,
    APPOINTMENT_PERMISSIONS.COMPLETE,
    APPOINTMENT_PERMISSIONS.POSTPONE,
    APPOINTMENT_PERMISSIONS.CALENDAR_VIEW,
  ],
  
  // Manager permissions (can manage team appointments)
  MANAGER: [
    APPOINTMENT_PERMISSIONS.CREATE,
    APPOINTMENT_PERMISSIONS.READ,
    APPOINTMENT_PERMISSIONS.UPDATE,
    APPOINTMENT_PERMISSIONS.DELETE,
    APPOINTMENT_PERMISSIONS.LIST,
    APPOINTMENT_PERMISSIONS.SEARCH,
    APPOINTMENT_PERMISSIONS.CONFIRM,
    APPOINTMENT_PERMISSIONS.COMPLETE,
    APPOINTMENT_PERMISSIONS.CANCEL,
    APPOINTMENT_PERMISSIONS.POSTPONE,
    APPOINTMENT_PERMISSIONS.CALENDAR_VIEW,
    APPOINTMENT_PERMISSIONS.ORG_VIEW_ALL,
  ],
  
  // Admin permissions (full access)
  ADMIN: [
    APPOINTMENT_PERMISSIONS.CREATE,
    APPOINTMENT_PERMISSIONS.READ,
    APPOINTMENT_PERMISSIONS.UPDATE,
    APPOINTMENT_PERMISSIONS.DELETE,
    APPOINTMENT_PERMISSIONS.LIST,
    APPOINTMENT_PERMISSIONS.SEARCH,
    APPOINTMENT_PERMISSIONS.CONFIRM,
    APPOINTMENT_PERMISSIONS.COMPLETE,
    APPOINTMENT_PERMISSIONS.CANCEL,
    APPOINTMENT_PERMISSIONS.POSTPONE,
    APPOINTMENT_PERMISSIONS.CALENDAR_VIEW,
    APPOINTMENT_PERMISSIONS.ADMIN,
    APPOINTMENT_PERMISSIONS.ORG_VIEW_ALL,
    APPOINTMENT_PERMISSIONS.ORG_MANAGE_ALL,
  ],
} as const;

/**
 * Get permissions for a role
 */
export function getAppointmentPermissions(role: keyof typeof APPOINTMENT_PERMISSION_GROUPS): string[] {
  return APPOINTMENT_PERMISSION_GROUPS[role] || [];
}
