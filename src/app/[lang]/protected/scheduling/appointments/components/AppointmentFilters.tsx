"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X } from "lucide-react";
import { AppointmentStatus } from "../lib/types";
import { APPOINTMENT_CONFIG } from "../lib/config";

/**
 * Filter component for appointment list
 * Provides filtering by status, date, and search
 */
export function AppointmentFilters() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [status, setStatus] = useState(searchParams.get("status") || "");
  const [date, setDate] = useState(searchParams.get("date") || "");
  const [search, setSearch] = useState(searchParams.get("search") || "");

  // Update URL when filters change
  const updateFilters = () => {
    const params = new URLSearchParams();
    
    if (status) params.set("status", status);
    if (date) params.set("date", date);
    if (search) params.set("search", search);
    
    // Reset to page 1 when filters change
    params.set("page", "1");
    
    const queryString = params.toString();
    const newUrl = queryString ? `?${queryString}` : "";
    
    router.push(newUrl);
  };

  // Clear all filters
  const clearFilters = () => {
    setStatus("");
    setDate("");
    setSearch("");
    router.push("");
  };

  // Check if any filters are active
  const hasActiveFilters = status || date || search;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Search appointments..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  updateFilters();
                }
              }}
            />
          </div>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger id="status">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All statuses</SelectItem>
              {Object.entries(APPOINTMENT_CONFIG.statuses).map(([key, config]) => (
                <SelectItem key={key} value={key}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date Filter */}
        <div className="space-y-2">
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <Label>&nbsp;</Label>
          <div className="flex gap-2">
            <Button onClick={updateFilters} className="flex-1">
              Apply
            </Button>
            {hasActiveFilters && (
              <Button variant="outline" onClick={clearFilters}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          {status && (
            <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded">
              Status: {APPOINTMENT_CONFIG.statuses[status as AppointmentStatus]?.label || status}
            </span>
          )}
          {date && (
            <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded">
              Date: {new Date(date).toLocaleDateString()}
            </span>
          )}
          {search && (
            <span className="text-sm bg-primary/10 text-primary px-2 py-1 rounded">
              Search: "{search}"
            </span>
          )}
        </div>
      )}
    </div>
  );
}
