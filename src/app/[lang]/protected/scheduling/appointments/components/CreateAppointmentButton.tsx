"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { Plus } from "lucide-react";
import { AppointmentForm } from "./AppointmentForm";

interface CreateAppointmentButtonProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

/**
 * Button component for creating new appointments
 * Opens a modal with the appointment form
 */
export function CreateAppointmentButton({
  variant = "default",
  size = "default",
  className,
}: CreateAppointmentButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        className={className}
        onClick={() => setIsModalOpen(true)}
      >
        <Plus className="h-4 w-4 mr-2" />
        New Appointment
      </Button>

      <Modal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Create New Appointment"
        description="Schedule a new appointment"
      >
        <AppointmentForm
          mode="create"
          onSuccess={() => setIsModalOpen(false)}
          onCancel={() => setIsModalOpen(false)}
        />
      </Modal>
    </>
  );
}
