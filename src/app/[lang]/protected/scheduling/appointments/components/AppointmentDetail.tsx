import { AppointmentWithDetails } from "../lib/types";
import { AppointmentStatusBadge } from "./AppointmentStatusBadge";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, FileText, User, Building } from "lucide-react";

interface AppointmentDetailProps {
  appointment: AppointmentWithDetails;
}

/**
 * Detailed view component for appointments
 * Displays all appointment information in a structured layout
 */
export function AppointmentDetail({ appointment }: AppointmentDetailProps) {
  return (
    <div className="space-y-6">
      {/* Header Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Basic Information</h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <FileText className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium">{appointment.title}</p>
                  {appointment.description && (
                    <p className="text-sm text-muted-foreground mt-1">{appointment.description}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <AppointmentStatusBadge status={appointment.status} />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold mb-2">Schedule</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Date</p>
                  <p className="font-medium">{appointment.date_display}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Time</p>
                  <p className="font-medium">{appointment.time_display}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-muted-foreground" />
                <div>
                  <p className="text-sm text-muted-foreground">Duration</p>
                  <p className="font-medium">{appointment.duration_display}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Timestamps */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">Timeline</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Created</p>
            <p className="text-sm font-medium">
              {new Date(appointment.created_at!).toLocaleString()}
            </p>
          </div>

          {appointment.updated_at && appointment.updated_at !== appointment.created_at && (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Last Updated</p>
              <p className="text-sm font-medium">
                {new Date(appointment.updated_at).toLocaleString()}
              </p>
            </div>
          )}

          {appointment.confirmed_at && (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Confirmed</p>
              <p className="text-sm font-medium">
                {new Date(appointment.confirmed_at).toLocaleString()}
              </p>
            </div>
          )}

          {appointment.completed_at && (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Completed</p>
              <p className="text-sm font-medium">
                {new Date(appointment.completed_at).toLocaleString()}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* System Information */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Appointment ID</p>
            <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{appointment.id}</p>
          </div>

          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Organization ID</p>
            <p className="text-sm font-mono bg-muted px-2 py-1 rounded">
              {appointment.organization_id}
            </p>
          </div>
        </div>
      </div>

      {/* Integration Status */}
      <div className="border-t pt-6">
        <h3 className="text-lg font-semibold mb-4">Integration Status</h3>
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">Self-Contained</Badge>
          {!appointment.case_file_id && <Badge variant="outline">No Case File Link</Badge>}
          {!appointment.service_id && <Badge variant="outline">No Service Link</Badge>}
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          This appointment is operating in self-contained mode and can be integrated with other
          systems later.
        </p>
      </div>
    </div>
  );
}
