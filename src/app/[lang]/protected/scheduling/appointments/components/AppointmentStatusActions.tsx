"use client";

import { useState, useTransition } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Modal } from "@/components/ui/modal";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Trash2,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { updateAppointmentStatus } from "../actions/update";
import { deleteAppointmentAndReturn } from "../actions/delete";
import { AppointmentWithDetails, AppointmentStatus } from "../lib/types";
import { APPOINTMENT_CONFIG, getAllowedStatusTransitions } from "../lib/config";

interface AppointmentStatusActionsProps {
  appointment: AppointmentWithDetails;
}

/**
 * Action buttons for appointment status management
 * Provides quick actions for status changes and deletion
 */
export function AppointmentStatusActions({ appointment }: AppointmentStatusActionsProps) {
  const [isPending, startTransition] = useTransition();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const allowedTransitions = getAllowedStatusTransitions(appointment.status);

  const handleStatusChange = (newStatus: AppointmentStatus) => {
    setError(null);
    startTransition(async () => {
      try {
        const result = await updateAppointmentStatus(appointment.id, newStatus);
        if (!result.success) {
          setError(result.error || "Failed to update status");
        }
        // On success, the page will revalidate automatically
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const handleDelete = () => {
    setError(null);
    startTransition(async () => {
      try {
        const result = await deleteAppointmentAndReturn(appointment.id);
        if (result.success) {
          setShowDeleteModal(false);
          // Redirect will happen automatically
        } else {
          setError(result.error || "Failed to delete appointment");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const getStatusIcon = (status: AppointmentStatus) => {
    switch (status) {
      case "confirmed":
        return <CheckCircle className="h-4 w-4" />;
      case "completed":
        return <CheckCircle className="h-4 w-4" />;
      case "cancelled":
        return <XCircle className="h-4 w-4" />;
      case "postponed":
        return <Calendar className="h-4 w-4" />;
      case "in_progress":
        return <Clock className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (allowedTransitions.length === 0) {
    return null; // No actions available
  }

  return (
    <>
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" disabled={isPending}>
            {isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <MoreHorizontal className="h-4 w-4" />
            )}
            Actions
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {allowedTransitions.map((status) => {
            const statusConfig = APPOINTMENT_CONFIG.statuses[status];
            return (
              <DropdownMenuItem
                key={status}
                onClick={() => handleStatusChange(status)}
                disabled={isPending}
              >
                {getStatusIcon(status)}
                <span className="ml-2">Mark as {statusConfig.label}</span>
              </DropdownMenuItem>
            );
          })}

          {allowedTransitions.length > 0 && (
            <DropdownMenuItem
              onClick={() => setShowDeleteModal(true)}
              disabled={isPending}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
              <span className="ml-2">Delete Appointment</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Appointment"
        description="Are you sure you want to delete this appointment? This action cannot be undone."
        footer={
          <div className="flex justify-end gap-2 w-full">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={isPending}>
              {isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="bg-muted p-4 rounded-lg">
            <h4 className="font-medium">{appointment.title}</h4>
            <p className="text-sm text-muted-foreground">
              {appointment.date_display} at {appointment.time_display}
            </p>
          </div>

          <p className="text-sm text-muted-foreground">
            This will permanently delete the appointment and all associated data.
          </p>
        </div>
      </Modal>
    </>
  );
}
