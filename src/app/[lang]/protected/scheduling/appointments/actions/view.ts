"use server";

import { auth } from "@/lib/auth/services/AuthenticationService";
import { AppointmentService } from "../lib/services/AppointmentService";
import { AppointmentWithDetails } from "../lib/types";

/**
 * Get appointment by ID
 */
export async function viewAppointment(id: string): Promise<{
  success: boolean;
  data?: AppointmentWithDetails;
  error?: any;
  message: string;
}> {
  try {
    // Validate authentication
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return {
        success: false,
        message: "Authentication required",
      };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organization_id) {
      return {
        success: false,
        message: "Organization context required",
      };
    }

    // Get appointment using service
    const appointmentService = new AppointmentService();
    const result = await appointmentService.getAppointmentById(id);

    if (!result.success) {
      return {
        success: false,
        error: result.error,
        message: result.message,
      };
    }

    // Verify appointment belongs to user's organization
    if (result.data.organization_id !== userProfile.organization_id) {
      return {
        success: false,
        message: "Appointment not found",
      };
    }

    return {
      success: true,
      data: result.data,
      message: "Appointment fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      error,
      message: error instanceof Error ? error.message : "Failed to fetch appointment",
    };
  }
}

/**
 * Check if user can view appointment
 */
export async function canViewAppointment(id: string): Promise<boolean> {
  try {
    const result = await viewAppointment(id);
    return result.success;
  } catch {
    return false;
  }
}
