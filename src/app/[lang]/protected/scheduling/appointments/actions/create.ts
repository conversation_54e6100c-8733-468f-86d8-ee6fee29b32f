"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { AppointmentService } from "../lib/services/AppointmentService";
import { AppointmentFormData } from "../lib/types";
import { APPOINTMENT_CONFIG } from "../lib/config";

// Validation schema for appointment creation
const createAppointmentSchema = z.object({
  title: z
    .string()
    .min(APPOINTMENT_CONFIG.validation.title.minLength, "Title is too short")
    .max(APPOINTMENT_CONFIG.validation.title.maxLength, "Title is too long"),
  description: z
    .string()
    .max(APPOINTMENT_CONFIG.validation.description.maxLength, "Description is too long")
    .optional(),
  appointment_date: z.string().min(1, "Date is required"),
  start_time: z.string().min(1, "Start time is required"),
  end_time: z.string().min(1, "End time is required"),
  status: z.enum([
    "planned",
    "confirmed",
    "in_progress",
    "completed",
    "missed",
    "postponed",
    "cancelled",
  ]),
});

/**
 * Create a new appointment
 */
export async function createAppointment(formData: FormData) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      throw new Error("Authentication required");
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Extract and validate form data
    const rawData = {
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      appointment_date: formData.get("appointment_date") as string,
      start_time: formData.get("start_time") as string,
      end_time: formData.get("end_time") as string,
      status: formData.get("status") as string,
    };

    const validatedData = createAppointmentSchema.parse(rawData);

    // Validate time logic
    const startTimeValidation = new Date(`2000-01-01T${validatedData.start_time}`);
    const endTimeValidation = new Date(`2000-01-01T${validatedData.end_time}`);

    if (endTimeValidation <= startTimeValidation) {
      throw new Error("End time must be after start time");
    }

    // Validate appointment date is not in the past (unless same day)
    const appointmentDate = new Date(validatedData.appointment_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (appointmentDate < today) {
      throw new Error("Cannot create appointments in the past");
    }

    // Calculate duration in minutes
    const startTimeDuration = new Date(`2000-01-01T${validatedData.start_time}`);
    const endTimeDuration = new Date(`2000-01-01T${validatedData.end_time}`);
    const durationMinutes = Math.round((endTimeDuration.getTime() - startTimeDuration.getTime()) / (1000 * 60));

    // Create appointment using service
    const appointmentService = new AppointmentService();
    const result = await appointmentService.createAppointment({
      title: validatedData.title,
      description: validatedData.description || null,
      appointment_date: validatedData.appointment_date,
      start_time: validatedData.start_time,
      end_time: validatedData.end_time,
      duration_minutes: durationMinutes,
      status: validatedData.status,
      organization_id: userProfile.organizationId,
      created_by: currentUser.id,
    });

    if (!result.success) {
      throw new Error(result.message);
    }

    // Revalidate relevant paths
    revalidatePath("/protected/scheduling/appointments");
    revalidatePath("/protected/scheduling/appointments/list");
    revalidatePath("/protected/scheduling/appointments/calendar");

    // Redirect to the new appointment
    redirect(`/protected/scheduling/appointments/${result.data?.id}/view`);
  } catch (error) {
    // Return error for form handling
    if (error instanceof z.ZodError) {
      const fieldErrors = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      throw new Error(`Validation failed: ${fieldErrors}`);
    }

    throw error;
  }
}

/**
 * Create appointment and return to list (for modal/drawer usage)
 */
export async function createAppointmentAndReturnToList(formData: FormData) {
  try {
    // Get current user and organization
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return { success: false, error: "Authentication required" };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Extract and validate form data
    const rawData = {
      title: formData.get("title") as string,
      description: formData.get("description") as string,
      appointment_date: formData.get("appointment_date") as string,
      start_time: formData.get("start_time") as string,
      end_time: formData.get("end_time") as string,
      status: formData.get("status") as string,
    };

    const validatedData = createAppointmentSchema.parse(rawData);

    // Validate time logic
    const startTimeValidation2 = new Date(`2000-01-01T${validatedData.start_time}`);
    const endTimeValidation2 = new Date(`2000-01-01T${validatedData.end_time}`);

    if (endTimeValidation2 <= startTimeValidation2) {
      return { success: false, error: "End time must be after start time" };
    }

    // Calculate duration in minutes
    const startTimeCalc = new Date(`2000-01-01T${validatedData.start_time}`);
    const endTimeCalc = new Date(`2000-01-01T${validatedData.end_time}`);
    const durationMinutes = Math.round((endTimeCalc.getTime() - startTimeCalc.getTime()) / (1000 * 60));

    // Create appointment using service
    const appointmentService = new AppointmentService();
    const result = await appointmentService.createAppointment({
      title: validatedData.title,
      description: validatedData.description || null,
      appointment_date: validatedData.appointment_date,
      start_time: validatedData.start_time,
      end_time: validatedData.end_time,
      duration_minutes: durationMinutes,
      status: validatedData.status,
      organization_id: userProfile.organizationId,
      created_by: currentUser.id,
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/scheduling/appointments");
    revalidatePath("/protected/scheduling/appointments/list");
    revalidatePath("/protected/scheduling/appointments/calendar");

    return {
      success: true,
      data: result.data,
      message: "Appointment created successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldErrors = error.errors
        .map((err) => `${err.path.join(".")}: ${err.message}`)
        .join(", ");
      return { success: false, error: `Validation failed: ${fieldErrors}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create appointment",
    };
  }
}
