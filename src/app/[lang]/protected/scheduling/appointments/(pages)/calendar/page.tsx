import { Suspense } from "react";
import { listAppointments } from "../../actions/list";
import { AppointmentCalendar } from "../../components/AppointmentCalendar";
import { CreateAppointmentButton } from "../../components/CreateAppointmentButton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { List, Calendar } from "lucide-react";
import Link from "next/link";

interface AppointmentCalendarPageProps {
  searchParams: {
    date?: string;
    view?: string;
  };
}

/**
 * Appointment Calendar Page
 * Displays all appointments in a calendar format using ScheduleCalendar component
 */
export default async function AppointmentCalendarPage({
  searchParams,
}: AppointmentCalendarPageProps) {
  const result = await listAppointments({
    date: searchParams.date,
    // Get more appointments for calendar view
    limit: 100,
  });

  if (!result.success) {
    throw new Error(result.message);
  }

  if (!result.data) {
    throw new Error("No data returned");
  }

  const { appointments } = result.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Appointment Calendar</h1>
          <p className="text-muted-foreground">View all appointments in calendar format</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="../list">
              <List className="h-4 w-4 mr-2" />
              List View
            </Link>
          </Button>
          <CreateAppointmentButton />
        </div>
      </div>

      {/* Calendar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Schedule Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="p-6">Loading calendar...</div>}>
            <AppointmentCalendar appointments={appointments} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
