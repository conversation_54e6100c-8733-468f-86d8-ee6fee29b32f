import { Suspense } from "react";
import { notFound } from "next/navigation";
import { viewAppointment } from "../../../actions/view";
import { AppointmentForm } from "../../../components/AppointmentForm";
import { <PERSON>, <PERSON>Content, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit } from "lucide-react";
import Link from "next/link";

interface AppointmentEditPageProps {
  params: {
    id: string;
  };
}

/**
 * Appointment Edit Page
 * Allows editing of an existing appointment
 */
export default async function AppointmentEditPage({ params }: AppointmentEditPageProps) {
  const result = await viewAppointment(params.id);

  if (!result.success) {
    if (result.message.includes("not found")) {
      notFound();
    }
    throw new Error(result.message);
  }

  const appointment = result.data;

  if (!appointment) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="../view">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Details
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Appointment</h1>
            <p className="text-muted-foreground">Update appointment information</p>
          </div>
        </div>
      </div>

      {/* Edit Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Appointment Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="p-6">Loading form...</div>}>
            <AppointmentForm appointment={appointment} mode="edit" />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
