import { Suspense } from "react";
import { notFound } from "next/navigation";
import { viewAppointment } from "../../../actions/view";
import { AppointmentDetail } from "../../../components/AppointmentDetail";
import { AppointmentStatusActions } from "../../../components/AppointmentStatusActions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Edit, Calendar } from "lucide-react";
import Link from "next/link";

interface AppointmentViewPageProps {
  params: {
    id: string;
  };
}

/**
 * Appointment View Page
 * Displays detailed information about a specific appointment
 */
export default async function AppointmentViewPage({ params }: AppointmentViewPageProps) {
  const result = await viewAppointment(params.id);

  if (!result.success) {
    if (result.message.includes("not found")) {
      notFound();
    }
    throw new Error(result.message);
  }

  const appointment = result.data;

  if (!appointment) {
    notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="../../list">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to List
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{appointment.title}</h1>
            <p className="text-muted-foreground">Appointment Details</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="../edit">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Link>
          </Button>
          <AppointmentStatusActions appointment={appointment} />
        </div>
      </div>

      {/* Appointment Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Appointment Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="p-6">Loading appointment details...</div>}>
            <AppointmentDetail appointment={appointment} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
