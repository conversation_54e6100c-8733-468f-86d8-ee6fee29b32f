import { Suspense } from "react";
import { listAppointments } from "../../actions/list";
import { AppointmentTable } from "../../components/AppointmentTable";
import { CreateAppointmentButton } from "../../components/CreateAppointmentButton";
import { AppointmentFilters } from "../../components/AppointmentFilters";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, List } from "lucide-react";
import Link from "next/link";

interface AppointmentListPageProps {
  searchParams: {
    status?: string;
    date?: string;
    search?: string;
    page?: string;
  };
}

/**
 * Appointment List Page
 * Displays all appointments in a table format with filtering options
 */
export default async function AppointmentListPage({ searchParams }: AppointmentListPageProps) {
  const result = await listAppointments({
    status: searchParams.status as any,
    date: searchParams.date,
    search: searchParams.search,
    page: parseInt(searchParams.page || "1"),
  });

  if (!result.success) {
    throw new Error(result.message);
  }

  if (!result.data) {
    throw new Error("No data returned");
  }

  const { appointments, pagination } = result.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Appointments</h1>
          <p className="text-muted-foreground">Manage and view all appointments</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" asChild>
            <Link href="../calendar">
              <Calendar className="h-4 w-4 mr-2" />
              Calendar View
            </Link>
          </Button>
          <CreateAppointmentButton />
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <List className="h-5 w-5" />
            Filter Appointments
          </CardTitle>
        </CardHeader>
        <CardContent>
          <AppointmentFilters />
        </CardContent>
      </Card>

      {/* Appointments Table */}
      <Card>
        <CardContent className="p-0">
          <Suspense fallback={<div className="p-6">Loading appointments...</div>}>
            <AppointmentTable appointments={appointments} pagination={pagination} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
