"use server";

import { revalidate<PERSON>ath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { RequestService } from "../lib/services/RequestService";
import { RequestInsert, RequestStatus } from "../lib/types";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

// Define the schema for request creation
const createRequestSchema = z.object({
  service_type: z.string().min(1, "Service type is required"),
});

// Type for the form data
type CreateRequestFormData = z.infer<typeof createRequestSchema>;

/**
 * Create a new request
 * @param formData The form data for the request
 * @returns ActionState with the created request or error
 */
export const createRequest = requirePermission(DOMAIN_PERMISSIONS.CREATE)(async (
  formData: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Extract and validate form data
    const rawData = Object.fromEntries(formData.entries());
    const validationResult = createRequestSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.format();
      return {
        success: false,
        error: "Validation failed: " + JSON.stringify(errors),
        data: null,
      };
    }

    const data = validationResult.data;

    // Get current user and organization
    const user = await auth.getCurrentUser();
    if (!user) {
      return {
        success: false,
        error: "User not authenticated",
        data: null,
      };
    }

    const organization = await ProfileService.getCurrentOrganization();
    if (!organization) {
      return {
        success: false,
        error: "Organization not found",
        data: null,
      };
    }

    // Prepare request data
    const requestData: RequestInsert = {
      service_id: data.service_type, // Using service_type as service_id
      status: "draft" as RequestStatus,
      requester_id: user.id,
      organization_id: organization.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Store additional data in metadata if needed
    // This would be handled by the RequestService

    // Create the request
    const response = await RequestService.create(requestData);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to create request",
        data: null,
      };
    }

    // Revalidate the requests list page
    revalidatePath("/[lang]/protected/request");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error creating request: ${error}`);
    return {
      success: false,
      error: `Unexpected error creating request: ${error}`,
      data: null,
    };
  }
});

/**
 * Create a new request and redirect to the request page
 * @param formData The form data for the request
 */
export const createRequestAndRedirect = requirePermission(DOMAIN_PERMISSIONS.CREATE)(async (
  formData: FormData,
  lang: string = "en"
) => {
  const result = await createRequest(formData);

  if (result.success && result.data) {
    // Redirect to the request view page
    redirect(`/${lang}/protected/request/${result.data.id}`);
  }

  return result;
});
