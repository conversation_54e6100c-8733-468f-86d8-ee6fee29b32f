"use client";

import React from "react";
import { HistoryTimeline, HistoryItem, HistoryAction } from "@/components/ui/history-timeline";
import { RequestHistory, RequestStatus } from "../lib/types";

interface RequestHistoryTimelineProps {
  /**
   * The request history entries
   */
  history: RequestHistory[];

  /**
   * Dictionary for translations
   */
  dictionary: any;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * RequestHistoryTimeline component
 * Displays a timeline of request history entries
 */
export function RequestHistoryTimeline({
  history,
  dictionary,
  className = "",
}: RequestHistoryTimelineProps) {
  // Convert request history to history items
  const historyItems: HistoryItem[] = history.map((entry) => {
    // Determine the action type
    let action: HistoryAction = "update";
    if (entry.previous_status === null && entry.new_status) {
      action = "create";
    } else if (entry.new_status === "completed" || entry.new_status === "closed") {
      action = "delete"; // Using delete to represent completion or closing
    }

    // Format the changes
    const changes = [];

    // Add status change if applicable
    if (entry.previous_status !== entry.new_status) {
      changes.push({
        field: dictionary?.fields?.status || "Status",
        oldValue: entry.previous_status
          ? dictionary?.statuses?.[entry.previous_status] &&
            typeof dictionary.statuses[entry.previous_status] === "string"
            ? dictionary.statuses[entry.previous_status]
            : formatStatus(entry.previous_status)
          : null,
        newValue: entry.new_status
          ? dictionary?.statuses?.[entry.new_status] &&
            typeof dictionary.statuses[entry.new_status] === "string"
            ? dictionary.statuses[entry.new_status]
            : formatStatus(entry.new_status)
          : null,
      });
    }

    // Add other changes from the changes object
    if (entry.changes) {
      Object.entries(entry.changes).forEach(([field, change]) => {
        // Skip status as we've already handled it
        if (field === "status") return;

        // Format field name
        const formattedField = formatFieldName(field, dictionary);

        // Format values based on field type
        let oldValue = formatValue(field, change.old, dictionary);
        let newValue = formatValue(field, change.new, dictionary);

        changes.push({
          field: formattedField,
          oldValue,
          newValue,
        });
      });
    }

    // Create a summary
    let summary = "";
    if (action === "create") {
      summary = dictionary?.historyActions?.created || "Request created";
    } else if (entry.new_status === "completed") {
      summary = dictionary?.historyActions?.completed || "Request completed";
    } else if (entry.new_status === "closed") {
      summary = dictionary?.historyActions?.closed || "Request closed";
    } else if (entry.previous_status !== entry.new_status) {
      summary = dictionary?.historyActions?.statusChanged || "Status changed";
    } else {
      summary = dictionary?.historyActions?.updated || "Request updated";
    }

    return {
      id: entry.id,
      timestamp: entry.created_at,
      user: {
        id: entry.user_id || "system",
        name: dictionary?.system || "System",
        avatarUrl: undefined,
      },
      action,
      changes,
      summary,
    };
  });

  return (
    <HistoryTimeline
      title={dictionary?.historyTitle || "Request History"}
      items={historyItems}
      className={className}
    />
  );
}

/**
 * Format a status value for display
 */
function formatStatus(status: RequestStatus | null): string {
  if (!status) return "-";
  return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, " ");
}

/**
 * Format a field name for display
 */
function formatFieldName(field: string, dictionary: any): string {
  // Check if the field name is in the dictionary
  if (dictionary?.fields?.[field]) {
    return dictionary.fields[field];
  }

  // Otherwise, format it from the camelCase or snake_case
  return field
    .replace(/_/g, " ")
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
}

/**
 * Format a value based on its field type
 */
function formatValue(field: string, value: any, dictionary: any): string {
  if (value === null || value === undefined) return "-";

  // Format dates with consistent output for server and client
  if (field.includes("date") || field.includes("_at")) {
    try {
      const date = new Date(value);
      // Use explicit formatting to ensure consistency between server and client
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (e) {
      return String(value);
    }
  }

  // Format booleans
  if (typeof value === "boolean") {
    return value ? dictionary?.yes || "Yes" : dictionary?.no || "No";
  }

  // Format arrays
  if (Array.isArray(value)) {
    return value.join(", ");
  }

  // Format objects
  if (typeof value === "object") {
    return JSON.stringify(value);
  }

  // Format status
  if (field === "status" && dictionary?.statuses?.[value]) {
    const status = dictionary.statuses[value];
    return typeof status === "string" ? status : String(value);
  }

  // Format priority
  if (field === "priority" && dictionary?.priorities?.[value]) {
    const priority = dictionary.priorities[value];
    return typeof priority === "string" ? priority : String(value);
  }

  // Format service type
  if (field === "service_type" && dictionary?.serviceTypes?.[value]) {
    const serviceType = dictionary.serviceTypes[value];
    return typeof serviceType === "string" ? serviceType : String(value);
  }

  // Default to string representation
  return String(value);
}
