"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import DOMAIN_CONFIG from "../lib/config/domain";

interface CreateRequestButtonProps {
  lang: string;
  dictionary?: any;
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
}

/**
 * Create Request Button
 * A button that links to the request creation wizard
 */
export function CreateRequestButton({
  lang,
  dictionary,
  className,
  variant = "default",
  size = "default",
}: CreateRequestButtonProps) {
  return (
    <Button asChild className={cn(className)} variant={variant} size={size}>
      <Link href={`/${lang}/protected/automation/request-wizard`}>
        <PlusCircle className="mr-2 h-4 w-4" />
        {dictionary?.createTitle || "Create New Request"}
      </Link>
    </Button>
  );
}
