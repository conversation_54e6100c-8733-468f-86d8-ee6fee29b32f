"use client";

import { TableCell, TableRow } from "@/components/ui/table";
import { Request } from "../lib/types";
import { RequestStatusBadge } from "./RequestStatusBadge";
import DOMAIN_CONFIG from "../lib/config/domain";

interface RequestTableRowProps {
  request: Request;
  lang: string;
  dictionary: any;
}

/**
 * RequestTableRow component
 * Displays a single request in a table row
 */
export function RequestTableRow({ request, lang, dictionary }: RequestTableRowProps) {
  // Format date in a human-friendly way based on the user's language
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);

      // Use the user's language for date formatting
      const locale = lang === "fr" ? "fr-CA" : "en-US";

      // Format based on locale
      return new Intl.DateTimeFormat(locale, {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: lang !== "fr", // French typically uses 24-hour format
      }).format(date);
    } catch (error) {
      return dateString;
    }
  };

  // Get service name based on service_id
  const getServiceName = () => {
    const serviceId = request.service_id;

    // Hardcoded mapping for specific service IDs
    if (serviceId === "1197cec5-dd35-42b0-8dc1-27f8c75969f6") {
      return "Échange";
    }

    // If we have a service object with a name, use that
    if (request.service?.name) {
      return request.service.name;
    }

    // For demo purposes, show the service_id
    if (serviceId) {
      return `Service ${serviceId.substring(0, 8)}`;
    }

    return "-";
  };

  // Get location name based on location_id
  const getLocationName = () => {
    const locationId = request.location_id;

    // Hardcoded mapping for specific location IDs
    if (locationId === "00000000-0000-0000-0000-000000000002") {
      return "Centre-Ville";
    }

    // If we have a location object with a name, use that
    if (request.location?.name) {
      return request.location.name;
    }

    // For demo purposes, show a readable location name
    if (locationId) {
      return `Location ${locationId.substring(0, 8)}`;
    }

    return "-";
  };

  // Get contact names with relationship types, each on a new line
  const getContactNames = () => {
    // Check if we have request_contacts data directly on the request
    if (
      request.request_contacts &&
      Array.isArray(request.request_contacts) &&
      request.request_contacts.length > 0
    ) {
      return (
        <div className="flex flex-col space-y-1">
          {request.request_contacts.map((rc, index) => {
            let contactName = "";

            // Get the contact name
            if (rc.contact && rc.contact.name) {
              contactName = rc.contact.name;
            } else if (rc.contact && rc.contact.first_name && rc.contact.last_name) {
              contactName = `${rc.contact.first_name} ${rc.contact.last_name}`;
            } else if (rc.contact?.id) {
              contactName = `Contact ${rc.contact.id.substring(0, 6)}`;
            } else {
              contactName = "Unknown Contact";
            }

            // Get the relationship type (formatted nicely)
            const relationshipType = rc.relationship_type
              ? rc.relationship_type.replace(/_/g, " ").toLowerCase()
              : "";

            // Return the formatted contact info
            return (
              <div key={index} className="text-sm">
                <span className="font-medium">{contactName}</span>
                {relationshipType && (
                  <span className="text-muted-foreground ml-1">({relationshipType})</span>
                )}
              </div>
            );
          })}
        </div>
      );
    }

    // If we have contacts in the request
    if (request.contacts && Array.isArray(request.contacts) && request.contacts.length > 0) {
      return (
        <div className="flex flex-col space-y-1">
          {request.contacts.map((contact, index) => {
            let contactName = "";

            // Get the contact name
            if (contact.name) {
              contactName = contact.name;
            } else if (contact.first_name && contact.last_name) {
              contactName = `${contact.first_name} ${contact.last_name}`;
            } else if (contact.full_name) {
              contactName = contact.full_name;
            } else if (contact.id) {
              contactName = `Contact ${contact.id.substring(0, 6)}`;
            } else {
              contactName = "Unknown Contact";
            }

            // Return the formatted contact info (without relationship type since it's not available)
            return (
              <div key={index} className="text-sm">
                <span className="font-medium">{contactName}</span>
              </div>
            );
          })}
        </div>
      );
    }

    return "-";
  };

  // These functions have been removed as priority is no longer used

  return (
    <TableRow
      className="cursor-pointer hover:bg-muted/50"
      onClick={() => {
        window.location.href = `/${lang}${DOMAIN_CONFIG.basePath}/${request.id}`;
      }}
    >
      <TableCell className="font-medium">
        {request.reference_number
          ? `REF-${request.reference_number}`
          : `REQ-${request.id.substring(0, 6)}`}
      </TableCell>
      <TableCell>{getContactNames()}</TableCell>
      <TableCell>{getServiceName()}</TableCell>
      <TableCell>{getLocationName()}</TableCell>
      <TableCell>{formatDate(request.created_at)}</TableCell>
      <TableCell>
        <RequestStatusBadge status={request.status} dictionary={dictionary} size="sm" />
      </TableCell>
    </TableRow>
  );
}
