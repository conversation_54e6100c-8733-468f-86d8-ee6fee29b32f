import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  Request,
  RequestInsert,
  RequestUpdate,
  RequestListParams,
  RequestWithRelations,
  RequestMetadata,
  RequestStatus,
  RequestHistory,
  RequestContact,
} from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { RequestHistoryService } from "./RequestHistoryService";

/**
 * Service for managing requests
 * This service provides methods for CRUD operations on requests
 */
export class RequestService {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * List requests with filtering, pagination, and sorting
   * @param params Parameters for filtering, pagination, and sorting
   * @returns Service response with the list of requests and total count
   */
  static async list(
    params: RequestListParams = {}
  ): Promise<ServiceResponse<{ items: Request[]; total: number }>> {
    try {
      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }
      const organizationId = organization.id;

      const {
        page = 1,
        limit = 10,
        status,
        search,
        sortBy = "created_at",
        sortOrder = "desc",
        assignee_id,
        requester_id,
        from_date,
        to_date,
      } = params;

      const offset = (page - 1) * limit;

      const supabase = await createClient();
      // Query requests with related service, location, and contact data
      let query = supabase
        .from("requests")
        .select(
          `
          *,
          service:service_id(*),
          location:location_id(*),
          request_contacts!request_id(
            *,
            contact:contact_id(*)
          )
        `,
          {
            count: "exact",
          }
        )
        .eq("organization_id", organizationId);

      // Apply filters
      if (status) {
        if (Array.isArray(status)) {
          query = query.in("status", status);
        } else {
          query = query.eq("status", status);
        }
      }

      if (search) {
        // Search in text fields only (UUID fields don't support ilike)
        query = query.or(`reference_number.ilike.%${search}%,rejection_reason.ilike.%${search}%`);
      }

      if (assignee_id) {
        query = query.eq("assignee_id", assignee_id);
      }

      if (requester_id) {
        query = query.eq("requester_id", requester_id);
      }

      if (from_date) {
        query = query.gte("created_at", from_date);
      }

      if (to_date) {
        query = query.lte("created_at", to_date);
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === "asc" });

      // Apply pagination
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) {
        logger.error(`Error listing requests: ${error.message}`);
        return errorResponse(error, `Failed to list requests: ${error.message}`);
      }

      // Manually fetch requester and assignee information
      const requests = data as Request[];
      const requestsWithRelations: RequestWithRelations[] = [];

      // Get unique requester and assignee IDs
      const requesterIds = [
        ...new Set(requests.filter((r) => r.requester_id).map((r) => r.requester_id)),
      ];
      const assigneeIds = [
        ...new Set(requests.filter((r) => r.assignee_id).map((r) => r.assignee_id)),
      ];

      // Fetch user profiles for requesters and assignees
      let requesters: Record<string, { id: string; email: string; name?: string }> = {};
      let assignees: Record<string, { id: string; email: string; name?: string }> = {};

      if (requesterIds.length > 0) {
        const { data: requesterData, error: requesterError } = await supabase
          .from("user_profiles")
          .select("id, email, first_name, last_name")
          .in("id", requesterIds as string[]);

        if (!requesterError && requesterData) {
          requesters = requesterData.reduce(
            (acc, user) => {
              acc[user.id] = {
                id: user.id,
                email: user.email,
                name: `${user.first_name} ${user.last_name}`.trim(),
              };
              return acc;
            },
            {} as Record<string, { id: string; email: string; name?: string }>
          );
        }
      }

      if (assigneeIds.length > 0) {
        const { data: assigneeData, error: assigneeError } = await supabase
          .from("user_profiles")
          .select("id, email, first_name, last_name")
          .in("id", assigneeIds as string[]);

        if (!assigneeError && assigneeData) {
          assignees = assigneeData.reduce(
            (acc, user) => {
              acc[user.id] = {
                id: user.id,
                email: user.email,
                name: `${user.first_name} ${user.last_name}`.trim(),
              };
              return acc;
            },
            {} as Record<string, { id: string; email: string; name?: string }>
          );
        }
      }

      // Combine requests with requester and assignee information
      for (const request of requests) {
        const requestWithRelations: RequestWithRelations = {
          ...request,
        };

        if (request.requester_id && requesters[request.requester_id]) {
          requestWithRelations.requester = requesters[request.requester_id];
        }

        if (request.assignee_id && assignees[request.assignee_id]) {
          requestWithRelations.assignee = assignees[request.assignee_id];
        }

        requestsWithRelations.push(requestWithRelations);
      }

      return successResponse(
        {
          items: requestsWithRelations,
          total: count || 0,
        },
        "Successfully retrieved requests"
      );
    } catch (error) {
      logger.error(`Unexpected error listing requests: ${error}`);
      return errorResponse(error, `Unexpected error listing requests`);
    }
  }

  /**
   * Get a request by ID with optional related data
   * @param id The ID of the request
   * @param includeMetadata Whether to include metadata
   * @param includeHistory Whether to include history
   * @param includeContacts Whether to include contacts
   * @returns Service response with the request and related data
   */
  static async view(
    id: string,
    includeMetadata: boolean = false,
    includeHistory: boolean = false,
    includeContacts: boolean = false
  ): Promise<ServiceResponse<RequestWithRelations>> {
    try {
      const supabase = await createClient();

      // Get the request with related service, location, contacts, requester and assignee information
      const { data, error } = await supabase
        .from("requests")
        .select(
          `
          *,
          service:service_id(*),
          location:location_id(*),
          request_contacts!request_id(
            *,
            contact:contact_id(*)
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          // Record not found
          return errorResponse(error, `Request with ID ${id} not found`);
        }

        logger.error(`Error reading request: ${error.message}`);
        return errorResponse(error, `Failed to read request: ${error.message}`);
      }

      const request = data as Request;

      // Create the RequestWithRelations object
      const requestWithRelations: RequestWithRelations = {
        ...request,
      };

      // Get requester information if available
      if (request.requester_id) {
        const { data: requesterData, error: requesterError } = await supabase
          .from("user_profiles")
          .select("id, email, first_name, last_name")
          .eq("id", request.requester_id)
          .single();

        if (!requesterError && requesterData) {
          requestWithRelations.requester = {
            id: requesterData.id,
            email: requesterData.email,
            name: `${requesterData.first_name} ${requesterData.last_name}`.trim(),
          };
        }
      }

      // Get assignee information if available
      if (request.assignee_id) {
        const { data: assigneeData, error: assigneeError } = await supabase
          .from("user_profiles")
          .select("id, email, first_name, last_name")
          .eq("id", request.assignee_id)
          .single();

        if (!assigneeError && assigneeData) {
          requestWithRelations.assignee = {
            id: assigneeData.id,
            email: assigneeData.email,
            name: `${assigneeData.first_name} ${assigneeData.last_name}`.trim(),
          };
        }
      }

      // Get metadata if requested
      if (includeMetadata) {
        const { data: metadataData, error: metadataError } = await supabase
          .from("request_metadata")
          .select("*")
          .eq("request_id", id)
          .single();

        if (metadataError && metadataError.code !== "PGRST116") {
          logger.error(`Error reading request metadata: ${metadataError.message}`);
        } else if (metadataData) {
          requestWithRelations.metadata = metadataData as RequestMetadata;
        }
      }

      // Get history if requested
      if (includeHistory) {
        const { data: historyData, error: historyError } = await supabase
          .from("request_history")
          .select("*")
          .eq("request_id", id)
          .order("created_at", { ascending: false });

        if (historyError) {
          logger.error(`Error reading request history: ${historyError.message}`);
        } else {
          // Convert string status to RequestStatus type and changes to Record<string, any>
          const typedHistoryData = (historyData || []).map((item) => ({
            ...item,
            previous_status: item.previous_status as RequestStatus | null,
            new_status: item.new_status as RequestStatus | null,
            changes: item.changes ? JSON.parse(JSON.stringify(item.changes)) : null,
          })) as RequestHistory[];
          requestWithRelations.history = typedHistoryData;
        }
      }

      // Get contacts if requested
      if (includeContacts) {
        const { data: contactsData, error: contactsError } = await supabase
          .from("request_contacts")
          .select(
            `
            *,
            contact:contact_id(*)
          `
          )
          .eq("request_id", id);

        if (contactsError) {
          logger.error(`Error reading request contacts: ${contactsError.message}`);
        } else {
          // Process contact data
          const contacts: RequestContact[] = [];

          for (const contactRelation of contactsData || []) {
            try {
              if (contactRelation.contact) {
                // The contact data is already included in the response
                const contactData = contactRelation.contact;

                // Create a contact object with safe property access
                // The contacts table has a name field instead of first_name/last_name
                // and email/phone are JSONB fields
                const nameParts = (contactData.name || "").split(" ");
                const firstName = nameParts[0] || "";
                const lastName = nameParts.slice(1).join(" ") || "";

                const contact = {
                  id: contactData.id || "",
                  name: `${firstName} ${lastName}`.trim(),
                  email: contactData.email,
                  phone: contactData.phone,
                  address: undefined,
                };

                contacts.push({
                  ...contactRelation,
                  contact,
                } as RequestContact);
              } else {
                // If we can't get contact details, just add the relation
                contacts.push({
                  ...contactRelation,
                  contact: undefined,
                } as RequestContact);
              }
            } catch (error) {
              logger.error(`Error processing contact details: ${error}`);
              contacts.push({
                ...contactRelation,
                contact: undefined,
              } as RequestContact);
            }
          }

          requestWithRelations.contacts = contacts;
        }
      }

      return successResponse(requestWithRelations, "Successfully retrieved request");
    } catch (error) {
      logger.error(`Unexpected error reading request: ${error}`);
      return errorResponse(error, `Unexpected error reading request`);
    }
  }

  /**
   * Create a new request
   * @param request The request to create
   * @returns Service response with the created request
   */
  static async create(request: RequestInsert): Promise<ServiceResponse<Request>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.from("requests").insert(request).select().single();

      if (error) {
        logger.error(`Error creating request: ${error.message}`);
        return errorResponse(error, `Failed to create request: ${error.message}`);
      }

      // Track the creation in history
      await RequestHistoryService.addHistoryEntry({
        request_id: data.id,
        action: "request_created",
        notes: "Request created",
        changes: {
          ...Object.entries(request).reduce(
            (acc, [key, value]) => {
              if (key !== "updated_at" && key !== "created_at") {
                acc[key] = {
                  old: null,
                  new: value,
                };
              }
              return acc;
            },
            {} as Record<string, any>
          ),
        },
      });

      return successResponse(data as Request, "Successfully created request");
    } catch (error) {
      logger.error(`Unexpected error creating request: ${error}`);
      return errorResponse(error, `Unexpected error creating request`);
    }
  }

  /**
   * Update an existing request
   * @param id The ID of the request to update
   * @param request The updated request data
   * @returns Service response with the updated request
   */
  static async update(id: string, request: RequestUpdate): Promise<ServiceResponse<Request>> {
    try {
      const supabase = await createClient();

      // First, get the current request data for history tracking
      const { data: currentRequest, error: fetchError } = await supabase
        .from("requests")
        .select("*")
        .eq("id", id)
        .single();

      if (fetchError) {
        logger.error(`Error fetching current request: ${fetchError.message}`);
        return errorResponse(fetchError, `Failed to fetch current request: ${fetchError.message}`);
      }

      // Add updated_at timestamp if not provided
      const updateData = {
        ...request,
        updated_at: request.updated_at || new Date().toISOString(),
      };

      // Update the request
      const { data, error } = await supabase
        .from("requests")
        .update(updateData)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        logger.error(`Error updating request: ${error.message}`);
        return errorResponse(error, `Failed to update request: ${error.message}`);
      }

      // Track the changes in history
      await RequestHistoryService.createHistoryEntry(
        id,
        currentRequest as Request,
        updateData,
        "request_updated",
        "Request details updated"
      );

      return successResponse(data as Request, "Successfully updated request");
    } catch (error) {
      logger.error(`Unexpected error updating request: ${error}`);
      return errorResponse(error, `Unexpected error updating request`);
    }
  }

  /**
   * Create or update request metadata
   * @param requestId The ID of the request
   * @param metadata The metadata to store
   * @returns Service response with the created/updated metadata
   */
  static async createMetadata(
    requestId: string,
    metadata: Record<string, any>
  ): Promise<ServiceResponse<RequestMetadata>> {
    try {
      const supabase = await createClient();

      // Check if metadata already exists
      const { data: existingData, error: checkError } = await supabase
        .from("request_metadata")
        .select("*")
        .eq("request_id", requestId)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        logger.error(`Error checking existing metadata: ${checkError.message}`);
        return errorResponse(
          checkError,
          `Failed to check existing metadata: ${checkError.message}`
        );
      }

      let result;

      if (existingData) {
        // Update existing metadata
        const { data, error } = await supabase
          .from("request_metadata")
          .update({
            service_requirements: metadata.service_requirements || null,
            family_availability: metadata.family_availability || null,
            updated_at: new Date().toISOString(),
          })
          .eq("request_id", requestId)
          .select()
          .single();

        if (error) {
          logger.error(`Error updating request metadata: ${error.message}`);
          return errorResponse(error, `Failed to update request metadata: ${error.message}`);
        }

        result = data;
      } else {
        // Create new metadata
        const { data, error } = await supabase
          .from("request_metadata")
          .insert({
            request_id: requestId,
            service_requirements: metadata.service_requirements || null,
            family_availability: metadata.family_availability || null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();

        if (error) {
          logger.error(`Error creating request metadata: ${error.message}`);
          return errorResponse(error, `Failed to create request metadata: ${error.message}`);
        }

        result = data;
      }

      return successResponse(
        result as RequestMetadata,
        "Successfully created/updated request metadata"
      );
    } catch (error) {
      logger.error(`Unexpected error creating/updating request metadata: ${error}`);
      return errorResponse(error, `Unexpected error creating/updating request metadata`);
    }
  }

  /**
   * Delete a request
   * @param id The ID of the request to delete
   * @returns Service response
   */
  static async delete(id: string): Promise<ServiceResponse<null>> {
    try {
      const supabase = await createClient();

      // First, get the current request data for history tracking
      const { data: currentRequest, error: fetchError } = await supabase
        .from("requests")
        .select("*")
        .eq("id", id)
        .single();

      if (fetchError) {
        logger.error(`Error fetching request for deletion: ${fetchError.message}`);
        return errorResponse(
          fetchError,
          `Failed to fetch request for deletion: ${fetchError.message}`
        );
      }

      // Delete the request
      const { error } = await supabase.from("requests").delete().eq("id", id);

      if (error) {
        logger.error(`Error deleting request: ${error.message}`);
        return errorResponse(error, `Failed to delete request: ${error.message}`);
      }

      // Track the deletion in history (this will be stored in the history table even though the request is deleted)
      await RequestHistoryService.addHistoryEntry({
        request_id: id,
        action: "request_deleted",
        notes: "Request deleted",
        changes: {
          request: {
            old: currentRequest,
            new: null,
          },
        },
      });

      return successResponse(null, "Successfully deleted request");
    } catch (error) {
      logger.error(`Unexpected error deleting request: ${error}`);
      return errorResponse(error, `Unexpected error deleting request`);
    }
  }
}
