import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { Request, RequestHistory, RequestStatus, RequestUpdate } from "../types";
import { ServiceResponse, successResponse, errorResponse } from "@/lib/types/responses";

/**
 * Interface for history entry creation
 */
interface HistoryEntryData {
  request_id: string;
  previous_status?: RequestStatus | null;
  new_status?: RequestStatus | null;
  action: string;
  notes?: string | null;
  changes?: Record<string, any> | null;
}

/**
 * Interface for history retrieval parameters
 */
interface HistoryRetrievalParams {
  requestId: string;
  page?: number;
  limit?: number;
  action?: string | string[];
  fromDate?: string;
  toDate?: string;
  userId?: string;
  includeUserDetails?: boolean;
  sortOrder?: "asc" | "desc";
}

/**
 * Service for managing request history
 * This service provides methods for tracking and retrieving request history
 */
export class RequestHistoryService {
  /**
   * Private constructor to prevent direct instantiation
   * Use the static methods instead
   */
  private constructor() {}

  /**
   * Get history entries for a request with basic pagination
   * @param requestId The ID of the request
   * @param limit Maximum number of entries to return
   * @param offset Offset for pagination
   * @returns Service response with the history entries
   * @deprecated Use getHistoryByRequestId instead for more advanced filtering and pagination
   */
  static async getHistory(
    requestId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<ServiceResponse<{ items: RequestHistory[]; total: number }>> {
    try {
      const supabase = await createClient();

      // Use a direct query instead of RPC since the RPC function is not in the TypeScript types
      const { data, error, count } = await supabase
        .from("request_history")
        .select("*, user:user_id(id, email)")
        .eq("request_id", requestId)
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error fetching request history: ${error.message}`);
        return errorResponse(error, `Failed to fetch request history: ${error.message}`);
      }

      return successResponse(
        {
          items: data as RequestHistory[],
          total: count || 0,
        },
        "Successfully retrieved request history"
      );
    } catch (error) {
      logger.error(`Unexpected error fetching request history: ${error}`);
      return errorResponse(error, `Unexpected error fetching request history`);
    }
  }

  /**
   * Get history entries for a request with advanced filtering and pagination
   * @param params Parameters for filtering and pagination
   * @returns Service response with the history entries and formatted data for display
   */
  static async getHistoryByRequestId(
    params: HistoryRetrievalParams
  ): Promise<ServiceResponse<{ items: RequestHistory[]; total: number; formattedItems: any[] }>> {
    try {
      const {
        requestId,
        page = 1,
        limit = 20,
        action,
        fromDate,
        toDate,
        userId,
        includeUserDetails = true,
        sortOrder = "desc",
      } = params;

      // Calculate offset from page and limit
      const offset = (page - 1) * limit;

      const supabase = await createClient();

      // Start building the query
      let query = supabase
        .from("request_history")
        .select("*", { count: "exact" })
        .eq("request_id", requestId);

      // Apply filters if provided
      if (action) {
        if (Array.isArray(action)) {
          query = query.in("action", action);
        } else {
          query = query.eq("action", action);
        }
      }

      if (fromDate) {
        query = query.gte("created_at", fromDate);
      }

      if (toDate) {
        query = query.lte("created_at", toDate);
      }

      if (userId) {
        query = query.eq("user_id", userId);
      }

      // Apply sorting and pagination
      query = query
        .order("created_at", { ascending: sortOrder === "asc" })
        .range(offset, offset + limit - 1);

      // Execute the query
      const { data, error, count } = await query;

      if (error) {
        logger.error(`Error fetching request history: ${error.message}`);
        return errorResponse(error, `Failed to fetch request history: ${error.message}`);
      }

      // Fetch user details if needed
      let userDetails: Record<string, any> = {};

      if (includeUserDetails && data && data.length > 0) {
        // Extract unique user IDs
        const userIds = [
          ...new Set(data.filter((item) => item.user_id).map((item) => item.user_id)),
        ];

        if (userIds.length > 0) {
          // Fetch user details
          const { data: userData, error: userError } = await supabase
            .from("user_profiles")
            .select("id, first_name, last_name, email")
            .in("id", userIds.filter((id) => id !== null) as string[]);

          if (!userError && userData) {
            // Create a map of user details by ID
            userDetails = userData.reduce(
              (acc, user) => {
                acc[user.id] = user;
                return acc;
              },
              {} as Record<string, any>
            );
          } else if (userError) {
            logger.warn(`Error fetching user details: ${userError.message}`);
          }
        }
      }

      // Format the history entries for display
      const formattedItems = (data || []).map((item: any) => {
        // Format the changes for better display
        const formattedChanges = RequestHistoryService.formatChangesForDisplay(item.changes);

        // Format the user information
        const user = item.user_id && includeUserDetails ? userDetails[item.user_id] : null;
        const userInfo = user
          ? {
              id: user.id,
              name: `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email,
              email: user.email,
              avatar: null,
            }
          : null;

        // Format dates
        const createdAt = new Date(item.created_at).toLocaleString();

        // Format status changes
        const statusChange =
          item.previous_status !== item.new_status && item.new_status
            ? {
                from: item.previous_status,
                to: item.new_status,
              }
            : null;

        return {
          id: item.id,
          requestId: item.request_id,
          action: item.action,
          actionDisplay: RequestHistoryService.formatActionForDisplay(item.action),
          notes: item.notes,
          createdAt,
          createdAtRaw: item.created_at,
          user: userInfo,
          statusChange,
          changes: formattedChanges,
          rawData: item,
        };
      });

      return successResponse(
        {
          items: data as unknown as RequestHistory[],
          total: count || 0,
          formattedItems,
        },
        "Successfully retrieved request history"
      );
    } catch (error) {
      logger.error(`Unexpected error fetching request history: ${error}`);
      return errorResponse(error, `Unexpected error fetching request history`);
    }
  }

  /**
   * Create a history entry for a request update
   * @param requestId The ID of the request
   * @param oldData The previous request data
   * @param newData The updated request data
   * @param action The action that caused the update
   * @param notes Optional notes about the update
   * @returns Service response with the created history entry
   */
  static async createHistoryEntry(
    requestId: string,
    oldData: Partial<Request>,
    newData: Partial<RequestUpdate>,
    action: string,
    notes?: string
  ): Promise<ServiceResponse<RequestHistory>> {
    try {
      // Build changes object by comparing old and new data
      const changes: Record<string, { old: any; new: any }> = {};

      // Track all fields that have changed
      Object.keys(newData).forEach((key) => {
        // Skip updated_at as it's always changed
        if (key === "updated_at") return;

        // Only include fields that have actually changed
        if (oldData[key as keyof Request] !== newData[key as keyof RequestUpdate]) {
          changes[key] = {
            old: oldData[key as keyof Request],
            new: newData[key as keyof RequestUpdate],
          };
        }
      });

      // Determine status change if applicable
      const previousStatus = oldData.status as RequestStatus | undefined;
      const newStatus = newData.status as RequestStatus | undefined;

      // Create history entry
      const historyData: HistoryEntryData = {
        request_id: requestId,
        action,
        notes: notes || `Request updated`,
        changes,
      };

      // Add status change information if status has changed
      if (previousStatus && newStatus && previousStatus !== newStatus) {
        historyData.previous_status = previousStatus;
        historyData.new_status = newStatus;
      }

      // Add the history entry
      return await RequestHistoryService.addHistoryEntry(historyData);
    } catch (error) {
      logger.error(`Unexpected error creating history entry: ${error}`);
      return errorResponse(error, `Unexpected error creating history entry`);
    }
  }

  /**
   * Add a history entry for a request
   * @param entryData The history entry data
   * @returns Service response with the created history entry
   */
  static async addHistoryEntry(
    entryData: HistoryEntryData
  ): Promise<ServiceResponse<RequestHistory>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("request_history")
        .insert({
          request_id: entryData.request_id,
          previous_status: entryData.previous_status,
          new_status: entryData.new_status,
          action: entryData.action,
          notes: entryData.notes,
          changes: entryData.changes,
          user_id: (await supabase.auth.getUser()).data.user?.id,
        })
        .select()
        .single();

      if (error) {
        logger.error(`Error adding history entry: ${error.message}`);
        return errorResponse(error, `Failed to add history entry: ${error.message}`);
      }

      return successResponse(data as RequestHistory, "Successfully added history entry");
    } catch (error) {
      logger.error(`Unexpected error adding history entry: ${error}`);
      return errorResponse(error, `Unexpected error adding history entry`);
    }
  }

  /**
   * Compare two versions of a request
   * @param requestId The ID of the request
   * @param historyId1 The ID of the first history entry
   * @param historyId2 The ID of the second history entry
   * @returns Service response with the differences between the two versions
   */
  static async compareVersions(
    requestId: string,
    historyId1: string,
    historyId2: string
  ): Promise<ServiceResponse<Record<string, { old: any; new: any }>>> {
    try {
      const supabase = await createClient();

      // Get the two history entries
      const { data: historyEntries, error: historyError } = await supabase
        .from("request_history")
        .select("*")
        .in("id", [historyId1, historyId2])
        .eq("request_id", requestId);

      if (historyError) {
        logger.error(`Error fetching history entries: ${historyError.message}`);
        return errorResponse(
          historyError,
          `Failed to fetch history entries: ${historyError.message}`
        );
      }

      if (!historyEntries || historyEntries.length !== 2) {
        return errorResponse(null, "Could not find both history entries");
      }

      // Sort by created_at to determine which is older
      historyEntries.sort(
        (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      const [olderEntry, newerEntry] = historyEntries;

      // Extract changes from both entries
      const olderChanges = olderEntry.changes || {};
      const newerChanges = newerEntry.changes || {};

      // Combine changes to show differences
      const differences: Record<string, { old: any; new: any }> = {};

      // Add fields from older entry
      Object.entries(olderChanges).forEach(([key, value]) => {
        if (typeof value === "object" && value !== null && "new" in value) {
          differences[key] = { old: value.new, new: value.new };
        }
      });

      // Update with fields from newer entry
      Object.entries(newerChanges).forEach(([key, value]) => {
        if (typeof value === "object" && value !== null && "old" in value && "new" in value) {
          if (differences[key]) {
            differences[key].new = value.new;
          } else {
            differences[key] = { old: value.old, new: value.new };
          }
        }
      });

      return successResponse(differences, "Successfully compared request versions");
    } catch (error) {
      logger.error(`Unexpected error comparing request versions: ${error}`);
      return errorResponse(error, `Unexpected error comparing request versions`);
    }
  }

  /**
   * Compare changes between two request versions with enhanced formatting
   * @param requestId The ID of the request
   * @param historyId1 The ID of the first history entry
   * @param historyId2 The ID of the second history entry
   * @returns Service response with formatted differences between the two versions
   */
  static async compareChanges(
    requestId: string,
    historyId1: string,
    historyId2: string
  ): Promise<
    ServiceResponse<{
      differences: Record<string, { old: any; new: any }>;
      formattedDifferences: any[];
      metadata: {
        olderVersion: {
          id: string;
          timestamp: string;
          user: any;
        };
        newerVersion: {
          id: string;
          timestamp: string;
          user: any;
        };
      };
    }>
  > {
    try {
      const supabase = await createClient();

      // Get the two history entries
      const { data: historyEntries, error: historyError } = await supabase
        .from("request_history")
        .select("*")
        .in("id", [historyId1, historyId2])
        .eq("request_id", requestId);

      if (historyError) {
        logger.error(`Error fetching history entries: ${historyError.message}`);
        return errorResponse(
          historyError,
          `Failed to fetch history entries: ${historyError.message}`
        );
      }

      if (!historyEntries || historyEntries.length !== 2) {
        return errorResponse(null, "Could not find both history entries");
      }

      // Sort by created_at to determine which is older
      historyEntries.sort(
        (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      const [olderEntry, newerEntry] = historyEntries;

      // Fetch user details if needed
      let userDetails: Record<string, any> = {};

      if (historyEntries && historyEntries.length > 0) {
        // Extract unique user IDs
        const userIds = [
          ...new Set(historyEntries.filter((item) => item.user_id).map((item) => item.user_id)),
        ];

        if (userIds.length > 0) {
          // Fetch user details
          const { data: userData, error: userError } = await supabase
            .from("user_profiles")
            .select("id, first_name, last_name, email")
            .in("id", userIds.filter((id) => id !== null) as string[]);

          if (!userError && userData) {
            // Create a map of user details by ID
            userDetails = userData.reduce(
              (acc, user) => {
                acc[user.id] = user;
                return acc;
              },
              {} as Record<string, any>
            );
          } else if (userError) {
            logger.warn(`Error fetching user details: ${userError.message}`);
          }
        }
      }

      // Format user information
      const formatUser = (entry: any) => {
        if (!entry.user_id) return null;
        const user = userDetails[entry.user_id];
        if (!user) return null;

        return {
          id: user.id,
          name: `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email,
          email: user.email,
          avatar: null,
        };
      };

      // Create metadata about the versions being compared
      const metadata = {
        olderVersion: {
          id: olderEntry.id,
          timestamp: new Date(olderEntry.created_at).toLocaleString(),
          user: formatUser(olderEntry),
        },
        newerVersion: {
          id: newerEntry.id,
          timestamp: new Date(newerEntry.created_at).toLocaleString(),
          user: formatUser(newerEntry),
        },
      };

      // Get differences using the existing method
      const differencesResponse = await RequestHistoryService.compareVersions(
        requestId,
        historyId1,
        historyId2
      );

      if (!differencesResponse.success) {
        return errorResponse(null, differencesResponse.message);
      }

      const differences = differencesResponse.data;

      // Format the differences for display
      const formattedDifferences = RequestHistoryService.formatDifferencesForDisplay(
        differences || {}
      );

      return successResponse(
        {
          differences: differences || {},
          formattedDifferences,
          metadata,
        },
        "Successfully compared and formatted changes between request versions"
      );
    } catch (error) {
      logger.error(`Unexpected error comparing changes: ${error}`);
      return errorResponse(error, `Unexpected error comparing changes`);
    }
  }

  /**
   * Get history entries with user details
   * @param requestId The ID of the request
   * @param limit Maximum number of entries to return
   * @param offset Offset for pagination
   * @returns Service response with the history entries including user details
   */
  static async getHistoryWithUserDetails(
    requestId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<ServiceResponse<{ items: any[]; total: number }>> {
    try {
      const supabase = await createClient();

      // Get history entries
      const { data, error, count } = await supabase
        .from("request_history")
        .select("*", { count: "exact" })
        .eq("request_id", requestId)
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        logger.error(`Error fetching request history with user details: ${error.message}`);
        return errorResponse(
          error,
          `Failed to fetch request history with user details: ${error.message}`
        );
      }

      // Fetch user details if needed
      let userDetails: Record<string, any> = {};

      if (data && data.length > 0) {
        // Extract unique user IDs
        const userIds = [
          ...new Set(data.filter((item) => item.user_id).map((item) => item.user_id)),
        ];

        if (userIds.length > 0) {
          // Fetch user details
          const { data: userData, error: userError } = await supabase
            .from("user_profiles")
            .select("id, first_name, last_name, email")
            .in("id", userIds.filter((id) => id !== null) as string[]);

          if (!userError && userData) {
            // Create a map of user details by ID
            userDetails = userData.reduce(
              (acc, user) => {
                acc[user.id] = user;
                return acc;
              },
              {} as Record<string, any>
            );
          } else if (userError) {
            logger.warn(`Error fetching user details: ${userError.message}`);
          }
        }
      }

      // Format the history entries
      const formattedItems = (data || []).map((item: any) => {
        // Get user details
        const user = item.user_id ? userDetails[item.user_id] : null;

        return {
          ...item,
          created_at_formatted: new Date(item.created_at).toLocaleString(),
          user_details: user
            ? {
                id: user.id,
                email: user.email,
                name: `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email,
                avatar: null,
              }
            : null,
          changes_formatted: RequestHistoryService.formatChangesForDisplay(
            item.changes as Record<string, any> | null
          ),
        };
      });

      return successResponse(
        {
          items: formattedItems,
          total: count || 0,
        },
        "Successfully retrieved request history with user details"
      );
    } catch (error) {
      logger.error(`Unexpected error fetching request history with user details: ${error}`);
      return errorResponse(error, `Unexpected error fetching request history with user details`);
    }
  }

  /**
   * Get a specific history entry
   * @param historyId The ID of the history entry
   * @returns Service response with the history entry
   */
  static async getHistoryEntry(historyId: string): Promise<ServiceResponse<RequestHistory>> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase
        .from("request_history")
        .select("*")
        .eq("id", historyId)
        .single();

      if (error) {
        logger.error(`Error fetching history entry: ${error.message}`);
        return errorResponse(error, `Failed to fetch history entry: ${error.message}`);
      }

      return successResponse(data as RequestHistory, "Successfully retrieved history entry");
    } catch (error) {
      logger.error(`Unexpected error fetching history entry: ${error}`);
      return errorResponse(error, `Unexpected error fetching history entry`);
    }
  }

  /**
   * Format changes for display
   * @param changes The changes object from the history entry
   * @returns Formatted changes for display
   */
  private static formatChangesForDisplay(changes: Record<string, any> | null): any[] {
    if (!changes) return [];

    return Object.entries(changes).map(([field, value]) => {
      // Handle nested objects (old/new format)
      if (value && typeof value === "object" && "old" in value && "new" in value) {
        return {
          field,
          fieldDisplay: RequestHistoryService.formatFieldNameForDisplay(field),
          oldValue: value.old,
          newValue: value.new,
          displayValue: RequestHistoryService.formatValueForDisplay(field, value.old, value.new),
        };
      }

      // Handle simple values
      return {
        field,
        fieldDisplay: RequestHistoryService.formatFieldNameForDisplay(field),
        value,
        displayValue: value,
      };
    });
  }

  /**
   * Format field names for display
   * @param fieldName The field name
   * @returns Formatted field name for display
   */
  private static formatFieldNameForDisplay(fieldName: string): string {
    // Convert snake_case to Title Case
    return fieldName
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }

  /**
   * Format values for display
   * @param fieldName The field name
   * @param oldValue The old value
   * @param newValue The new value
   * @returns Formatted value for display
   */
  private static formatValueForDisplay(fieldName: string, oldValue: any, newValue: any): string {
    // Handle dates
    if (
      fieldName.includes("date") ||
      fieldName.includes("_at") ||
      fieldName === "created_at" ||
      fieldName === "updated_at"
    ) {
      const oldDate = oldValue ? new Date(oldValue).toLocaleString() : "None";
      const newDate = newValue ? new Date(newValue).toLocaleString() : "None";
      return `${oldDate} → ${newDate}`;
    }

    // Handle status changes
    if (fieldName === "status") {
      return `${oldValue || "None"} → ${newValue || "None"}`;
    }

    // Handle boolean values
    if (typeof oldValue === "boolean" || typeof newValue === "boolean") {
      const oldDisplay = oldValue === true ? "Yes" : oldValue === false ? "No" : "None";
      const newDisplay = newValue === true ? "Yes" : newValue === false ? "No" : "None";
      return `${oldDisplay} → ${newDisplay}`;
    }

    // Handle null/undefined values
    const oldDisplay = oldValue === null || oldValue === undefined ? "None" : String(oldValue);
    const newDisplay = newValue === null || newValue === undefined ? "None" : String(newValue);

    return `${oldDisplay} → ${newDisplay}`;
  }

  /**
   * Format action for display
   * @param action The action string
   * @returns Formatted action for display
   */
  private static formatActionForDisplay(action: string): string {
    switch (action) {
      case "request_created":
        return "Request Created";
      case "request_updated":
        return "Request Updated";
      case "request_deleted":
        return "Request Deleted";
      case "status_change":
        return "Status Changed";
      default:
        // Convert snake_case to Title Case
        return action
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
    }
  }

  /**
   * Format differences for display
   * @param differences The differences object from compareVersions
   * @returns Formatted differences for display
   */
  private static formatDifferencesForDisplay(
    differences: Record<string, { old: any; new: any }>
  ): any[] {
    return Object.entries(differences).map(([field, value]) => {
      const { old: oldValue, new: newValue } = value;

      // Determine if the field is a complex object
      const isComplexOld = oldValue && typeof oldValue === "object" && !Array.isArray(oldValue);
      const isComplexNew = newValue && typeof newValue === "object" && !Array.isArray(newValue);

      // Format the field name
      const fieldDisplay = RequestHistoryService.formatFieldNameForDisplay(field);

      // Format the display value
      const displayValue = RequestHistoryService.formatValueForDisplay(field, oldValue, newValue);

      // Determine the change type
      let changeType = "modified";
      if (oldValue === null || oldValue === undefined) {
        changeType = "added";
      } else if (newValue === null || newValue === undefined) {
        changeType = "removed";
      } else if (JSON.stringify(oldValue) === JSON.stringify(newValue)) {
        changeType = "unchanged";
      }

      // Handle complex objects (like nested JSON)
      let nestedChanges = null;
      if (isComplexOld && isComplexNew) {
        nestedChanges = RequestHistoryService.compareComplexObjects(oldValue, newValue);
      }

      return {
        field,
        fieldDisplay,
        oldValue,
        newValue,
        displayValue,
        changeType,
        nestedChanges,
        // Add additional metadata for UI rendering
        metadata: {
          isDate: field.includes("date") || field.includes("_at"),
          isBoolean: typeof oldValue === "boolean" || typeof newValue === "boolean",
          isStatus: field === "status",
          isComplex: isComplexOld || isComplexNew,
          hasNestedChanges: nestedChanges !== null && nestedChanges.length > 0,
        },
      };
    });
  }

  /**
   * Compare complex objects and identify differences
   * @param oldObj The old object
   * @param newObj The new object
   * @returns Array of differences in nested properties
   */
  private static compareComplexObjects(oldObj: any, newObj: any): any[] {
    if (!oldObj || !newObj) {
      return [];
    }

    const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);
    const differences: any[] = [];

    allKeys.forEach((key) => {
      const oldValue = oldObj[key];
      const newValue = newObj[key];

      // Skip if values are the same
      if (JSON.stringify(oldValue) === JSON.stringify(newValue)) {
        return;
      }

      // Determine change type
      let changeType = "modified";
      if (!(key in oldObj) || oldValue === null || oldValue === undefined) {
        changeType = "added";
      } else if (!(key in newObj) || newValue === null || newValue === undefined) {
        changeType = "removed";
      }

      // Format the field name
      const fieldDisplay = RequestHistoryService.formatFieldNameForDisplay(key);

      // Handle nested objects recursively
      let nestedChanges = null;
      if (
        oldValue &&
        newValue &&
        typeof oldValue === "object" &&
        typeof newValue === "object" &&
        !Array.isArray(oldValue) &&
        !Array.isArray(newValue)
      ) {
        nestedChanges = RequestHistoryService.compareComplexObjects(oldValue, newValue);
      }

      differences.push({
        field: key,
        fieldDisplay,
        oldValue,
        newValue,
        displayValue: RequestHistoryService.formatValueForDisplay(key, oldValue, newValue),
        changeType,
        nestedChanges,
        metadata: {
          isDate: key.includes("date") || key.includes("_at"),
          isBoolean: typeof oldValue === "boolean" || typeof newValue === "boolean",
          isComplex:
            (oldValue && typeof oldValue === "object") ||
            (newValue && typeof newValue === "object"),
          hasNestedChanges: nestedChanges !== null && nestedChanges.length > 0,
        },
      });
    });

    return differences;
  }

  /**
   * Get a detailed comparison between two request versions
   * @param requestId The ID of the request
   * @param versionA The ID of the first version (history entry)
   * @param versionB The ID of the second version (history entry)
   * @returns Service response with detailed comparison results
   */
  static async getDetailedComparison(
    requestId: string,
    versionA: string,
    versionB: string
  ): Promise<
    ServiceResponse<{
      comparison: any;
      metadata: any;
      summary: {
        totalChanges: number;
        addedFields: string[];
        removedFields: string[];
        modifiedFields: string[];
      };
    }>
  > {
    try {
      // Get the comparison data
      const comparisonResponse = await RequestHistoryService.compareChanges(
        requestId,
        versionA,
        versionB
      );

      if (!comparisonResponse.success || !comparisonResponse.data) {
        return errorResponse(null, comparisonResponse.message || "Failed to get comparison data");
      }

      const { differences, formattedDifferences, metadata } = comparisonResponse.data;

      // Generate a summary of changes
      const addedFields: string[] = [];
      const removedFields: string[] = [];
      const modifiedFields: string[] = [];

      formattedDifferences.forEach((diff: any) => {
        switch (diff.changeType) {
          case "added":
            addedFields.push(diff.fieldDisplay);
            break;
          case "removed":
            removedFields.push(diff.fieldDisplay);
            break;
          case "modified":
            modifiedFields.push(diff.fieldDisplay);
            break;
        }
      });

      const summary = {
        totalChanges: formattedDifferences.length,
        addedFields,
        removedFields,
        modifiedFields,
      };

      // Get the request details
      const supabase = await createClient();
      const { data: request, error: requestError } = await supabase
        .from("requests")
        .select("*")
        .eq("id", requestId)
        .single();

      if (requestError) {
        logger.error(`Error fetching request details: ${requestError.message}`);
      }

      // Enhance the comparison with request details
      const comparison = {
        request: request || null,
        differences: formattedDifferences,
        rawDifferences: differences,
      };

      return successResponse(
        {
          comparison,
          metadata,
          summary,
        },
        "Successfully generated detailed comparison"
      );
    } catch (error) {
      logger.error(`Unexpected error generating detailed comparison: ${error}`);
      return errorResponse(error, `Unexpected error generating detailed comparison`);
    }
  }
}
