import { notFound } from "next/navigation";
import { RequestDetail } from "../../components/RequestDetail";
import { i18n } from "@/lib/i18n/services/I18nService";
import { getRequestWithAllRelations } from "../../actions";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Home, FileText, Calendar, User, Tag } from "lucide-react";
import Link from "next/link";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import DOMAIN_CONFIG from "../../lib/config/domain";
import { RequestStatusBadge } from "../../components/RequestStatusBadge";
import { RequestStatusActions } from "../../components/RequestStatusActions";
import { H1, Lead } from "@/components/typography";

interface ViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

// Helper function to format dates in a human-friendly way based on language
const formatDate = (dateString: string, lang: string): string => {
  try {
    const date = new Date(dateString);

    // Use the user's language for date formatting
    const locale = lang === "fr" ? "fr-CA" : "en-US";

    // Format based on locale with a more human-friendly format
    return new Intl.DateTimeFormat(locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: lang !== "fr", // French typically uses 24-hour format
    }).format(date);
  } catch (error) {
    return dateString;
  }
};

/**
 * View page for a specific Request item
 * Uses the RequestDetail component to display the request details
 */
export default async function ViewPage({ params }: ViewPageProps) {
  // Await the params
  const { lang, id } = await params;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);
  const requestDictionary = dictionary.request;

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Get the request data with all relations
  const response = await getRequestWithAllRelations(id);

  // If the request doesn't exist, show the not found page
  if (!response.success || !response.data) {
    notFound();
  }

  const request = response.data;

  // Check if the user has permission to edit the request
  // For now, we'll just check if the user is the creator or if they're in the same organization
  const canEdit =
    request.requester_id === user.id ||
    request.organization_id === (await auth.getCurrentUserOrganizationId());

  // Define permissions for request actions
  // For now, allow all actions if the user can edit the request
  const permissions = {
    canApprove: canEdit,
    canReject: canEdit,
    canComplete: canEdit,
    canWaitlist: canEdit,
    canRequest: canEdit,
  };

  // Format dates
  const createdAt = request.created_at ? formatDate(request.created_at, lang) : "-";

  return (
    <div className="space-y-6">
      {/* Breadcrumb and Back Button */}
      <div className="flex items-center justify-between mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${lang}/protected/dashboard`}>
                <Home className="h-4 w-4" />
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/${lang}${DOMAIN_CONFIG.basePath}/list`}>
                {requestDictionary?.title || "Requests"}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>{requestDictionary?.viewTitle || "View"}</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <Button variant="outline" size="sm" asChild>
          <Link href={`/${lang}${DOMAIN_CONFIG.basePath}/list`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            {requestDictionary?.backToList || "Back to List"}
          </Link>
        </Button>
      </div>

      {/* Request Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div className="flex items-center">
          <div className="bg-primary/10 p-2 rounded-full mr-3">
            <FileText className="h-10 w-10 text-primary" />
          </div>
          <div>
            <H1>{requestDictionary?.title || "Request"}</H1>
            <Lead className="mt-1">
              {request.reference_number || `REQ-${request.id.substring(0, 6)}`}
            </Lead>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <RequestStatusBadge
            status={request.status}
            dictionary={dictionary}
            size="lg"
            showTooltip
          />
          <RequestStatusActions
            request={request}
            dictionary={dictionary}
            permissions={permissions}
            lang={lang}
          />
        </div>
      </div>

      {/* Key Information Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {/* Reference Number */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Reference Number
              </CardTitle>
              <Tag className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium truncate">
              {request.reference_number || `REQ-${request.id.substring(0, 6)}`}
            </div>
          </CardContent>
        </Card>

        {/* Service */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {requestDictionary?.fields?.serviceType || "Service"}
              </CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium truncate">
              {request.service?.name || requestDictionary?.notSpecified || "Not specified"}
            </div>
          </CardContent>
        </Card>

        {/* Created By */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {requestDictionary?.fields?.requester || "Created By"}
              </CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium truncate">
              {request.requester?.name ||
                request.requester?.email?.split("@")[0] ||
                requestDictionary?.notSpecified ||
                "Not specified"}
            </div>
          </CardContent>
        </Card>

        {/* Created Date */}
        <Card>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {requestDictionary?.fields?.createdAt || "Created"}
              </CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium">
              {createdAt.split(",")[0]}
              <div className="text-xs text-muted-foreground mt-1">
                {createdAt.split(",")[1]?.trim() || ""}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Request Details */}
      <Card>
        <CardHeader>
          <CardTitle>{requestDictionary?.details || "Request Details"}</CardTitle>
        </CardHeader>
        <CardContent>
          <RequestDetail request={request} dictionary={dictionary} lang={lang} canEdit={canEdit} />
        </CardContent>
      </Card>
    </div>
  );
}
