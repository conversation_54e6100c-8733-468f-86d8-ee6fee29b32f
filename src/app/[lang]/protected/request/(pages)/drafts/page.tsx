import { PageTitle } from "@/components/typography";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, FileText, Calendar, User } from "lucide-react";
import Link from "next/link";

import { DraftService } from "@/app/[lang]/protected/automation/lib/services/DraftService";
import { Draft } from "@/app/[lang]/protected/automation/lib/types/draft";

interface DraftRequestsPageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Draft Requests Page
 * Shows a list of draft requests that can be continued using the wizard
 */
export default async function DraftRequestsPage({ params }: DraftRequestsPageProps) {
  const { lang } = await params;

  // Get all draft requests for the organization
  const response = await DraftService.listDrafts({
    workflowType: "request_creation",
  });

  const draftRequests = response.success ? response.data || [] : [];

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <PageTitle description="Continue working on your draft requests">Draft Requests</PageTitle>
        <Button asChild>
          <Link href={`/${lang}/protected/automation/request-wizard`}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Request
          </Link>
        </Button>
      </div>

      {draftRequests.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-10">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No draft requests found</h3>
              <p className="text-muted-foreground mb-4">
                You don't have any draft requests. Start by creating a new request.
              </p>
              <Button asChild>
                <Link href={`/${lang}/protected/automation/request-wizard`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Request
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {draftRequests.map((draft) => (
            <Card key={draft.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <CardTitle className="text-lg">
                        Draft Request - {draft.id.substring(0, 8)}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-4 mt-1">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(draft.created_at).toLocaleDateString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          Step: {draft.current_step}
                        </span>
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="inline-flex items-center rounded-full bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800 ring-1 ring-inset ring-yellow-600/20">
                      Draft
                    </span>
                    <Button asChild>
                      <Link href={`/${lang}/protected/automation/wizard/${draft.id}/basic_info`}>
                        Continue
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
