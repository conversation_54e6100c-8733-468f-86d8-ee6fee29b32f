import { createServiceClient } from "@/lib/supabase/service";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  UserProfile,
  UserCreate,
  UserRoleUpdate,
  mapDbProfileToUserProfile,
  UserProfileRow,
} from "../types";
import { UserRole } from "@/lib/authorization/types";
import { RoleService } from "@/lib/authorization/services/RoleService";

/**
 * Service for administrative user management operations using service role key
 */
export class UserManagementService {
  /**
   * Get a user's profile by user ID
   * @param userId The user ID
   * @returns Promise resolving to the user's profile
   */
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      logger.info(`Getting profile for user: ${userId}`);

      const supabase = await createServiceClient();

      // Get the user profile from the database
      const { data, error } = await supabase
        .from("user_profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        logger.error(`Error fetching user profile: ${error.message}`);
        return null;
      }

      if (!data) {
        logger.warn(`No profile found for user ${userId}`);
        return null;
      }

      // Get role data from user_roles
      const { data: roleData, error: roleError } = await supabase
        .from("user_roles")
        .select("role, organization_id")
        .eq("user_id", userId)
        .single();

      if (roleError && !roleError.message.includes("No rows found")) {
        logger.error(`Error getting role data: ${roleError.message}`);
      }

      // Map database profile to application profile
      const profile = mapDbProfileToUserProfile(data as UserProfileRow);

      // Add role information if available
      if (roleData) {
        profile.role = roleData.role as UserRole;
        if (!profile.organizationId && roleData.organization_id) {
          profile.organizationId = roleData.organization_id;
        }
      }

      return profile;
    } catch (error) {
      logger.error(`Error getting user profile: ${error}`);
      return null;
    }
  }

  /**
   * Get all users for an organization
   * @param organizationId The organization ID
   * @returns Promise resolving to an array of user profiles
   */
  static async getUsersByOrganization(organizationId: string): Promise<UserProfile[]> {
    try {
      logger.info(`Getting users for organization: ${organizationId}`);

      const supabase = await createServiceClient();

      // Get all user roles for the organization
      const { data: roleData, error: roleError } = await supabase
        .from("user_roles")
        .select("user_id, role")
        .eq("organization_id", organizationId);

      if (roleError) {
        logger.error(`Error getting user roles: ${roleError.message}`);
        return [];
      }

      if (!roleData || roleData.length === 0) {
        logger.info(`No users found for organization: ${organizationId}`);
        return [];
      }

      // Get profiles for all users
      const profiles: UserProfile[] = [];
      for (const role of roleData) {
        const profile = await this.getUserProfile(role.user_id);
        if (profile) {
          profiles.push(profile);
        }
      }

      return profiles;
    } catch (error) {
      logger.error(`Error getting users by organization: ${error}`);
      return [];
    }
  }

  /**
   * Create a new user
   * @param userData The user data
   * @returns Promise resolving to the created user profile
   */
  static async createUser(userData: UserCreate): Promise<UserProfile | null> {
    try {
      logger.info(`Creating user: ${userData.email}`);

      const supabase = await createServiceClient();

      // Create user in auth.users
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        email_confirm: true,
        password: "Password123!",
        user_metadata: {
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone,
          language: userData.language || "en",
          organization_id: userData.organizationId, // Include organization_id in metadata
          notification_preferences: {
            email: true,
            sms: false,
            inApp: true,
          },
        },
      });

      if (authError) {
        logger.error(`Error creating user: ${authError.message}`);
        return null;
      }

      if (!authData.user) {
        logger.error("User creation failed: No user returned");
        return null;
      }

      // Create user role
      const { error: roleError } = await supabase.from("user_roles").insert({
        user_id: authData.user.id,
        role: userData.role,
        organization_id: userData.organizationId,
      });

      if (roleError) {
        logger.error(`Error creating user role: ${roleError.message}`);
        // TODO: Should we delete the user if role creation fails?
        return null;
      }

      // Update the user profile with organization_id
      if (userData.organizationId) {
        const { error: profileError } = await supabase
          .from("user_profiles")
          .update({ organization_id: userData.organizationId })
          .eq("id", authData.user.id);

        if (profileError) {
          logger.error(`Error updating user profile with organization_id: ${profileError.message}`);
          // We'll continue even if this fails, as the user and role were created successfully
        } else {
          logger.info(`Updated user profile with organization_id: ${userData.organizationId}`);
        }
      }

      // Return the created user profile
      return this.getUserProfile(authData.user.id);
    } catch (error) {
      logger.error(`Error creating user: ${error}`);
      return null;
    }
  }

  /**
   * Update a user's profile information
   * @param userId The user ID
   * @param profileUpdate The profile update data
   * @returns Promise resolving to the updated user profile
   */
  static async updateUserProfile(
    userId: string,
    profileUpdate: Partial<UserProfile>
  ): Promise<UserProfile | null> {
    try {
      logger.info(`Updating profile for user: ${userId}`);

      const supabase = await createServiceClient();

      // Get the current user profile
      const { data: currentProfile, error: profileError } = await supabase
        .from("user_profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (profileError) {
        logger.error(`Error fetching user profile: ${profileError.message}`);
        return null;
      }

      if (!currentProfile) {
        logger.warn(`No profile found for user ${userId}`);
        return null;
      }

      // Update the profile
      const { error: updateError } = await supabase
        .from("user_profiles")
        .update({
          first_name: profileUpdate.firstName || currentProfile.first_name,
          last_name: profileUpdate.lastName || currentProfile.last_name,
          phone: profileUpdate.phone !== undefined ? profileUpdate.phone : currentProfile.phone,
        })
        .eq("id", userId)
        .select()
        .single();

      if (updateError) {
        logger.error(`Error updating user profile: ${updateError.message}`);
        return null;
      }

      return this.getUserProfile(userId);
    } catch (error) {
      logger.error(`Error updating user profile: ${error}`);
      return null;
    }
  }

  /**
   * Update a user's role
   * @param userId The user ID
   * @param roleUpdate The role update data
   * @returns Promise resolving to success or error
   */
  static async updateUserRole(
    userId: string,
    roleUpdate: UserRoleUpdate
  ): Promise<{ success: boolean; error?: string }> {
    try {
      logger.info(`Updating role for user: ${userId} to ${roleUpdate.role}`);

      // Validate role
      if (!RoleService.isValidRole(roleUpdate.role)) {
        return { success: false, error: "Invalid role" };
      }

      const supabase = await createServiceClient();

      // Check if user exists
      const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

      if (userError || !userData.user) {
        logger.error(`Error getting user: ${userError?.message || "User not found"}`);
        return { success: false, error: "User not found" };
      }

      // Get current role
      const { data: roleData, error: roleError } = await supabase
        .from("user_roles")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (roleError && !roleError.message.includes("No rows found")) {
        logger.error(`Error getting user role: ${roleError.message}`);
        return { success: false, error: "Error getting user role" };
      }

      // Update or insert role
      if (roleData) {
        // Update existing role
        const { error: updateError } = await supabase
          .from("user_roles")
          .update({ role: roleUpdate.role })
          .eq("user_id", userId);

        if (updateError) {
          logger.error(`Error updating user role: ${updateError.message}`);
          return { success: false, error: "Error updating user role" };
        }
      } else {
        // Insert new role
        const { error: insertError } = await supabase.from("user_roles").insert({
          user_id: userId,
          role: roleUpdate.role,
        });

        if (insertError) {
          logger.error(`Error creating user role: ${insertError.message}`);
          return { success: false, error: "Error creating user role" };
        }
      }

      return { success: true };
    } catch (error) {
      logger.error(`Error updating user role: ${error}`);
      return { success: false, error: "Unexpected error updating user role" };
    }
  }
}
