"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { saveContactInfo } from "@/app/[lang]/protected/automation-firstdraft/(features)/employee-wizard/actions";
import { ActionState } from "@/lib/types/responses/actionState";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { i18n } from "@/lib/i18n/services/I18nService";

interface ContactInfoFormProps {
  draftId: string;
  draftData: AutomationDraft;
  lang: string;
}

/**
 * Contact Info Form
 * Client component for the contact info step
 */
export function ContactInfoForm({ draftId, draftData, lang }: ContactInfoFormProps) {
  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Initial state for the edit action
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  // Create a server action for updating the draft
  const [state, formAction, pending] = useActionState(saveContactInfo, initialState);

  // Extract contact info from draft data
  const draftDataObj = (draftData.data as Record<string, any>) || {};
  const contactInfo = draftDataObj.contactInfo || {
    address: "",
    emails: [],
    phones: [],
  };

  // Extract email and phone from arrays
  const email =
    contactInfo.emails && contactInfo.emails.length > 0
      ? (contactInfo.emails[0] as any)?.email || ""
      : "";

  const phone =
    contactInfo.phones && contactInfo.phones.length > 0
      ? (contactInfo.phones[0] as any)?.number || ""
      : "";

  return (
    <form action={formAction} className="space-y-4">
      {/* Display error message if there is one */}
      {state.error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Hidden field for the draft ID */}
      <input type="hidden" name="id" value={draftId} />

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="address">
            {dictionary.employee?.wizard?.fields?.address || "Address"}
          </Label>
          <Textarea
            id="address"
            name="address"
            defaultValue={contactInfo.address}
            placeholder={dictionary.employee?.wizard?.placeholders?.enterAddress || "Enter address"}
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">{dictionary.employee?.wizard?.fields?.email || "Email"}</Label>
          <Input
            id="email"
            name="email"
            type="email"
            defaultValue={email}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterEmail || "Enter email address"
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">{dictionary.employee?.wizard?.fields?.phone || "Phone"}</Label>
          <Input
            id="phone"
            name="phone"
            defaultValue={phone}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterPhone || "Enter phone number"
            }
          />
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            (window.location.href = `/${lang}/protected/automation-firstdraft/employee-wizard/${draftId}/basic-info`)
          }
        >
          {dictionary.employee?.wizard?.buttons?.previousBasicInfo || "Previous: Basic Information"}
        </Button>
        <Button type="submit" disabled={pending}>
          {pending
            ? dictionary.common?.saving || "Saving..."
            : dictionary.employee?.wizard?.buttons?.nextEmploymentDetails ||
              "Next: Employment Details"}
        </Button>
      </div>
    </form>
  );
}
