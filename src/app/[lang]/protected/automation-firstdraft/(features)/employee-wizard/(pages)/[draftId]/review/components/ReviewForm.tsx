"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { H4, P } from "@/components/typography";
import { completeEmployeeCreation } from "@/app/[lang]/protected/automation-firstdraft/(features)/employee-wizard/actions";
import { ActionState } from "@/lib/types/responses/actionState";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { i18n } from "@/lib/i18n/services/I18nService";

interface ReviewFormProps {
  draftId: string;
  draftData: AutomationDraft;
  lang: string;
}

/**
 * Review Form
 * Client component for the review step
 */
export function ReviewForm({ draftId, draftData, lang }: ReviewFormProps) {
  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Initial state for the complete action
  const initialState: ActionState<any> = {
    success: true,
    error: "",
    data: null,
  };

  // Create a server action for completing the workflow
  const [state, formAction, pending] = useActionState(completeEmployeeCreation, initialState);

  // Extract data from the draft
  const draftDataObj = (draftData.data as Record<string, any>) || {};
  const basicInfo = draftDataObj.basicInfo || {};
  const contactInfo = draftDataObj.contactInfo || {};
  const employmentDetails = draftDataObj.employmentDetails || {};

  // Extract email and phone
  const email =
    contactInfo.emails && contactInfo.emails.length > 0 ? contactInfo.emails[0]?.email || "" : "";

  const phone =
    contactInfo.phones && contactInfo.phones.length > 0 ? contactInfo.phones[0]?.number || "" : "";

  return (
    <form action={formAction} className="space-y-6">
      {/* Display error message if there is one */}
      {state.error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Hidden field for the draft ID */}
      <input type="hidden" name="draftId" value={draftId} />

      <P className="text-muted-foreground">
        {dictionary.employee?.wizard?.reviewDescription ||
          "Please review the information below before submitting. You can go back to any step to make changes."}
      </P>

      <div className="space-y-4">
        <Card>
          <CardContent className="pt-6">
            <H4>{dictionary.employee?.wizard?.stepTitles?.basicInfo || "Basic Information"}</H4>
            <div className="grid grid-cols-2 gap-2 mt-2">
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.firstName || "First Name"}:
                </P>
                <P>{basicInfo.first_name || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.lastName || "Last Name"}:
                </P>
                <P>{basicInfo.last_name || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.jobTitle || "Job Title"}:
                </P>
                <P>{basicInfo.job_title || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.department || "Department"}:
                </P>
                <P>{basicInfo.department || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <H4>{dictionary.employee?.wizard?.stepTitles?.contactInfo || "Contact Information"}</H4>
            <div className="grid grid-cols-2 gap-2 mt-2">
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.address || "Address"}:
                </P>
                <P>{contactInfo.address || dictionary.common?.noAddressProvided || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.email || "Email"}:
                </P>
                <P>{email || dictionary.common?.noEmail || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.phone || "Phone"}:
                </P>
                <P>{phone || dictionary.common?.noPhone || "N/A"}</P>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <H4>
              {dictionary.employee?.wizard?.stepTitles?.employmentDetails || "Employment Details"}
            </H4>
            <div className="grid grid-cols-2 gap-2 mt-2">
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.employmentStatus || "Employment Status"}:
                </P>
                <P>
                  {employmentDetails.employment_status || dictionary.common?.notSpecified || "N/A"}
                </P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.hireDate || "Hire Date"}:
                </P>
                <P>{employmentDetails.hire_date || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.employeeId || "Employee ID"}:
                </P>
                <P>{employmentDetails.employee_id || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
              <div>
                <P className="font-medium">
                  {dictionary.employee?.wizard?.fields?.supervisorId || "Supervisor ID"}:
                </P>
                <P>{employmentDetails.supervisor_id || dictionary.common?.notSpecified || "N/A"}</P>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            (window.location.href = `/${lang}/protected/automation-firstdraft/employee-wizard/${draftId}/employment-details`)
          }
        >
          {dictionary.employee?.wizard?.buttons?.previousEmploymentDetails ||
            "Previous: Employment Details"}
        </Button>
        <Button type="submit" disabled={pending}>
          {pending
            ? dictionary.common?.submitting || "Submitting..."
            : dictionary.employee?.wizard?.buttons?.createEmployee || "Submit Employee"}
        </Button>
      </div>
    </form>
  );
}
