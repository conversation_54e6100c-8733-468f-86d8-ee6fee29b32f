"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { saveEmploymentDetails } from "@/app/[lang]/protected/automation-firstdraft/(features)/employee-wizard/actions";
import { ActionState } from "@/lib/types/responses/actionState";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { i18n } from "@/lib/i18n/services/I18nService";

interface EmploymentDetailsFormProps {
  draftId: string;
  draftData: AutomationDraft;
  lang: string;
}

/**
 * Employment Details Form
 * Client component for the employment details step
 */
export function EmploymentDetailsForm({ draftId, draftData, lang }: EmploymentDetailsFormProps) {
  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Initial state for the edit action
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  // Create a server action for updating the draft
  const [state, formAction, pending] = useActionState(saveEmploymentDetails, initialState);

  // Extract employment details from draft data
  const draftDataObj = (draftData.data as Record<string, any>) || {};
  const employmentDetails = draftDataObj.employmentDetails || {
    employment_status: "active",
    hire_date: "",
    employee_id: "",
    supervisor_id: "",
  };

  return (
    <form action={formAction} className="space-y-4">
      {/* Display error message if there is one */}
      {state.error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Hidden field for the draft ID */}
      <input type="hidden" name="id" value={draftId} />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="employment_status">
            {dictionary.employee?.wizard?.fields?.employmentStatus || "Employment Status"}
          </Label>
          <Select
            name="employment_status"
            defaultValue={employmentDetails.employment_status || "active"}
          >
            <SelectTrigger id="employment_status">
              <SelectValue
                placeholder={
                  dictionary.employee?.wizard?.placeholders?.selectEmploymentStatus ||
                  "Select status"
                }
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">
                {dictionary.employee?.management?.employmentStatus?.active || "Active"}
              </SelectItem>
              <SelectItem value="inactive">
                {dictionary.employee?.management?.employmentStatus?.inactive || "Inactive"}
              </SelectItem>
              <SelectItem value="terminated">
                {dictionary.employee?.management?.employmentStatus?.terminated || "Terminated"}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="hire_date">
            {dictionary.employee?.wizard?.fields?.hireDate || "Hire Date"}
          </Label>
          <Input
            id="hire_date"
            name="hire_date"
            type="date"
            defaultValue={employmentDetails.hire_date}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterHireDate || "Enter hire date"
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="employee_id">
            {dictionary.employee?.wizard?.fields?.employeeId || "Employee ID"}
          </Label>
          <Input
            id="employee_id"
            name="employee_id"
            defaultValue={employmentDetails.employee_id}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterEmployeeId || "Enter employee ID"
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="supervisor_id">
            {dictionary.employee?.wizard?.fields?.supervisorId || "Supervisor ID"}
          </Label>
          <Input
            id="supervisor_id"
            name="supervisor_id"
            defaultValue={employmentDetails.supervisor_id}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterSupervisorId || "Enter supervisor ID"
            }
          />
        </div>
      </div>

      <div className="flex justify-between mt-6">
        <Button
          type="button"
          variant="outline"
          onClick={() =>
            (window.location.href = `/${lang}/protected/automation-firstdraft/employee-wizard/${draftId}/contact-info`)
          }
        >
          {dictionary.employee?.wizard?.buttons?.previousContactInfo ||
            "Previous: Contact Information"}
        </Button>
        <Button type="submit" disabled={pending}>
          {pending
            ? dictionary.common?.saving || "Saving..."
            : dictionary.employee?.wizard?.buttons?.nextReview || "Next: Review"}
        </Button>
      </div>
    </form>
  );
}
