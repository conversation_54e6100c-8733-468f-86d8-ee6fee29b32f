import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { H3, P } from "@/components/typography";
import { CheckCircle } from "lucide-react";
import { i18n } from "@/lib/i18n/services/I18nService";

interface SuccessPageProps {
  params: Promise<{
    draftId: string;
    lang: string;
  }>;
}

/**
 * Success Page
 * Displayed after successful employee creation
 */
export default async function SuccessPage({ params }: SuccessPageProps) {
  const { lang } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="max-w-2xl mx-auto text-center space-y-6">
      <div className="flex justify-center">
        <CheckCircle className="h-16 w-16 text-green-500" />
      </div>

      <H3>{dictionary.employee?.wizard?.success?.title || "Employee Created Successfully!"}</H3>

      <P className="text-muted-foreground">
        {dictionary.employee?.wizard?.success?.description ||
          "The employee has been created successfully. You can now view the employee details or create another employee."}
      </P>

      {/* Execution details removed as they're not useful to the user */}

      <div className="flex justify-center gap-4 mt-8">
        <Button variant="outline" asChild>
          <Link href={`/${lang}/protected/employee/management`}>
            {dictionary.employee?.wizard?.success?.viewEmployee || "View Employees"}
          </Link>
        </Button>

        <Button asChild>
          <Link href={`/${lang}/protected/automation-firstdraft/employee-wizard`}>
            {dictionary.employee?.wizard?.success?.createAnother || "Create Another Employee"}
          </Link>
        </Button>
      </div>
    </div>
  );
}
