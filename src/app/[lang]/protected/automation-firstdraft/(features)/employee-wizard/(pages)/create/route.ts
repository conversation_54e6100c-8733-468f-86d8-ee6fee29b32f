"use server";

import { NextRequest, NextResponse } from "next/server";
import { create } from "@/app/[lang]/protected/automation-firstdraft/actions/drafts/create";
import { ActionState } from "@/lib/types/responses/actionState";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";

/**
 * Route handler for creating a new draft
 * This avoids the issue with revalidatePath during render
 */
export async function GET(request: NextRequest) {
  // Initial state for the create action
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  // Create a new draft
  const formData = new FormData();
  formData.append("workflowType", "employee_creation");
  formData.append("currentStep", "basic_info");
  formData.append("data", JSON.stringify({}));

  const result = await create(initialState, formData);

  if (result.success && result.data) {
    // Redirect to the first step with the draft ID
    return NextResponse.redirect(new URL(`${result.data.id}/basic-info`, request.url));
  } else {
    // Handle error
    return NextResponse.redirect(new URL(`?error=failed-to-create-draft`, request.url));
  }
}
