"use client";

import { useActionState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { saveBasicInfo } from "@/app/[lang]/protected/automation-firstdraft/(features)/employee-wizard/actions";
import { ActionState } from "@/lib/types/responses/actionState";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { i18n } from "@/lib/i18n/services/I18nService";

interface BasicInfoFormProps {
  draftId: string;
  draftData: AutomationDraft;
  lang: string;
}

/**
 * Basic Info Form
 * Client component for the basic info step
 */
export function BasicInfoForm({ draftId, draftData, lang }: BasicInfoFormProps) {
  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Initial state for the edit action
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  // Create a server action for updating the draft
  const [state, formAction, pending] = useActionState(saveBasicInfo, initialState);

  // Extract basic info from draft data
  const draftDataObj = (draftData.data as Record<string, any>) || {};
  const basicInfo = draftDataObj.basicInfo || {
    first_name: "",
    last_name: "",
    job_title: "",
    department: "",
  };

  return (
    <form action={formAction} className="space-y-4">
      {/* Display error message if there is one */}
      {state.error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Hidden field for the draft ID */}
      <input type="hidden" name="id" value={draftId} />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="first_name">
            {dictionary.employee?.wizard?.fields?.firstName || "First Name"}
          </Label>
          <Input
            id="first_name"
            name="first_name"
            defaultValue={basicInfo.first_name}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterFirstName || "Enter first name"
            }
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="last_name">
            {dictionary.employee?.wizard?.fields?.lastName || "Last Name"}
          </Label>
          <Input
            id="last_name"
            name="last_name"
            defaultValue={basicInfo.last_name}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterLastName || "Enter last name"
            }
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="job_title">
            {dictionary.employee?.wizard?.fields?.jobTitle || "Job Title"}
          </Label>
          <Input
            id="job_title"
            name="job_title"
            defaultValue={basicInfo.job_title}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.enterJobTitle || "Enter job title"
            }
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="department">
            {dictionary.employee?.wizard?.fields?.department || "Department"}
          </Label>
          <Input
            id="department"
            name="department"
            defaultValue={basicInfo.department}
            placeholder={
              dictionary.employee?.wizard?.placeholders?.selectDepartment || "Enter department"
            }
          />
        </div>
      </div>

      <div className="flex justify-end mt-6">
        <Button type="submit" disabled={pending}>
          {pending
            ? dictionary.common?.saving || "Saving..."
            : dictionary.employee?.wizard?.buttons?.nextContactInfo || "Next: Contact Information"}
        </Button>
      </div>
    </form>
  );
}
