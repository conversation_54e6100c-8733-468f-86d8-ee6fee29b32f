import { notFound } from "next/navigation";
import { StepWizard } from "@/components/ui/step-wizard";
import { H3 } from "@/components/typography";
import { view } from "@/app/[lang]/protected/automation-firstdraft/actions/drafts/view";
import { ActionState } from "@/lib/types/responses/actionState";
import { AutomationDraft } from "@/app/[lang]/protected/automation-firstdraft/lib/types";
import { EmploymentDetailsForm } from "./components/EmploymentDetailsForm";
import { i18n } from "@/lib/i18n/services/I18nService";

interface EmploymentDetailsPageProps {
  params: Promise<{
    draftId: string;
    lang: string;
  }>;
}

/**
 * Employment Details Step
 * Third step in the employee creation wizard
 */
export default async function EmploymentDetailsPage({ params }: EmploymentDetailsPageProps) {
  const { draftId, lang } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  // Initial state for the view action
  const initialState: ActionState<AutomationDraft> = {
    success: true,
    error: "",
    data: null,
  };

  // Fetch draft data server-side
  const result = await view(initialState, { id: draftId });
  if (!result.success || !result.data) {
    notFound();
  }

  const draftData = result.data;

  return (
    <StepWizard
      title={dictionary.employee?.wizard?.wizardTitle || "Create New Employee"}
      description={
        dictionary.employee?.wizard?.wizardDescription ||
        "Complete the following steps to create a new employee"
      }
      steps={[
        { label: dictionary.employee?.wizard?.steps?.basicInfo || "Basic Info", completed: true },
        {
          label: dictionary.employee?.wizard?.steps?.contactInfo || "Contact Info",
          completed: true,
        },
        {
          label: dictionary.employee?.wizard?.steps?.employmentDetails || "Employment Details",
          completed: false,
        },
        { label: dictionary.employee?.wizard?.steps?.review || "Review", completed: false },
      ]}
      currentStep={2}
      previousUrl={`/${lang}/protected/automation-firstdraft/employee-wizard/${draftId}/contact-info`}
      nextUrl="#"
    >
      <div className="space-y-6">
        <H3>
          {dictionary.employee?.wizard?.stepTitles?.employmentDetails || "Employment Details"}
        </H3>
        <EmploymentDetailsForm draftId={draftId} draftData={draftData} lang={lang} />
      </div>
    </StepWizard>
  );
}
