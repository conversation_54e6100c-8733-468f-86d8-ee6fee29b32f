"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  ActionState,
  errorActionState,
  successActionState,
} from "@/lib/types/responses/actionState";
import { DraftService } from "../../../lib/services";
import { AutomationDraft } from "../../../lib/types";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { AUTOMATION_PERMISSIONS } from "../../../lib/security/permissions";

/**
 * Save employment details step and redirect to the next step
 * @param prevState Previous state
 * @param formData Form data
 * @returns Result of the update operation
 */
export const saveEmploymentDetails = requirePermission(AUTOMATION_PERMISSIONS.UPDATE)(async (
  _prevState: ActionState<AutomationDraft>,
  formData: FormData
): Promise<ActionState<AutomationDraft>> => {
  // Get form data
  const draftId = formData.get("id") as string;
  const employmentStatus = formData.get("employment_status") as string;
  const hireDate = formData.get("hire_date") as string;
  const employeeId = formData.get("employee_id") as string;
  const supervisorId = formData.get("supervisor_id") as string;

  // Flag to track if we should redirect
  let shouldRedirect = false;
  let result;

  try {
    // Get current user
    const user = await auth.getCurrentUser();
    if (!user) {
      return errorActionState("User not authenticated");
    }

    // Validate required fields
    if (!draftId) {
      return errorActionState("Draft ID is required");
    }

    // Get the current draft
    const draftResult = await DraftService.getDraft(draftId);
    if (!draftResult.success || !draftResult.data) {
      logger.error(`Error getting draft: ${draftResult.error}`);
      return errorActionState(`Failed to get draft: ${draftResult.message}`);
    }

    // Get current data and merge with new data
    const currentData = (draftResult.data.data as Record<string, any>) || {};
    const updatedData = {
      ...currentData,
      employmentDetails: {
        employment_status: employmentStatus || "active",
        hire_date: hireDate,
        employee_id: employeeId,
        supervisor_id: supervisorId,
      },
    };

    // Create draft update object
    const draft = {
      current_step: "review",
      data: updatedData,
    };

    // Update draft
    result = await DraftService.updateDraft(draftId, draft);

    if (!result.success) {
      logger.error(`Error updating draft: ${result.error}`);
      return errorActionState(`Failed to update draft: ${result.message}`);
    }

    // Revalidate path
    revalidatePath("/[lang]/protected/automation-firstdraft");

    // Set redirect flag
    shouldRedirect = true;

    // Return success state (this will only be used if redirect doesn't happen)
    return successActionState(result.data);
  } catch (error) {
    logger.error(`Unexpected error updating employment details: ${error}`);
    return errorActionState("An unexpected error occurred while saving employment details");
  } finally {
    // Redirect outside of try-catch if successful
    if (shouldRedirect && result && result.data) {
      redirect(`/fr/protected/automation-firstdraft/employee-wizard/${draftId}/review`);
    }
  }
});
