import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { RequestStatusBadge } from "@/app/[lang]/protected/request/components/RequestStatusBadge";
import { i18n } from "@/lib/i18n/services/I18nService";
import { Separator } from "@/components/ui/separator";
import { H1, H2, H3, Muted } from "@/components/typography";

interface RequestStatusBadgePageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * RequestStatusBadge example page
 * Demonstrates the usage of the RequestStatusBadge component
 */
export default async function RequestStatusBadgePage({ params }: RequestStatusBadgePageProps) {
  const { lang } = await params;
  const dictionary = i18n.getDictionary(lang);

  // All possible request statuses
  const statuses = ["draft", "requested", "waitlist", "approved", "rejected", "completed"];

  return (
    <div className="container py-10">
      <div className="max-w-5xl mx-auto">
        <H1>Request Status Badge</H1>
        <Muted className="text-lg mt-2 mb-8">
          A component for displaying request status with appropriate colors and icons
        </Muted>

        <Separator className="my-6" />

        {/* Basic usage */}
        <section className="mb-10">
          <H2 className="mb-4">Basic Usage</H2>
          <Card>
            <CardHeader>
              <CardTitle>Default Status Badges</CardTitle>
              <CardDescription>
                The RequestStatusBadge component displays a badge with the request status, using
                appropriate colors and icons.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                {statuses.map((status) => (
                  <RequestStatusBadge key={status} status={status as any} dictionary={dictionary} />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Size variants */}
        <section className="mb-10">
          <H2 className="mb-4">Size Variants</H2>
          <Card>
            <CardHeader>
              <CardTitle>Different Sizes</CardTitle>
              <CardDescription>
                The RequestStatusBadge component supports different sizes: sm, md (default), and lg.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <H3 className="mb-2">Small (sm)</H3>
                  <div className="flex flex-wrap gap-4">
                    {statuses.map((status) => (
                      <RequestStatusBadge
                        key={status}
                        status={status as any}
                        dictionary={dictionary}
                        size="sm"
                      />
                    ))}
                  </div>
                </div>

                <div>
                  <H3 className="mb-2">Medium (md) - Default</H3>
                  <div className="flex flex-wrap gap-4">
                    {statuses.map((status) => (
                      <RequestStatusBadge
                        key={status}
                        status={status as any}
                        dictionary={dictionary}
                        size="md"
                      />
                    ))}
                  </div>
                </div>

                <div>
                  <H3 className="mb-2">Large (lg)</H3>
                  <div className="flex flex-wrap gap-4">
                    {statuses.map((status) => (
                      <RequestStatusBadge
                        key={status}
                        status={status as any}
                        dictionary={dictionary}
                        size="lg"
                      />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Without icons */}
        <section className="mb-10">
          <H2 className="mb-4">Without Icons</H2>
          <Card>
            <CardHeader>
              <CardTitle>Text-Only Badges</CardTitle>
              <CardDescription>
                The RequestStatusBadge component can be displayed without icons by setting the
                showIcon prop to false.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                {statuses.map((status) => (
                  <RequestStatusBadge
                    key={status}
                    status={status as any}
                    dictionary={dictionary}
                    showIcon={false}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Without tooltips */}
        <section className="mb-10">
          <H2 className="mb-4">Without Tooltips</H2>
          <Card>
            <CardHeader>
              <CardTitle>Badges Without Tooltips</CardTitle>
              <CardDescription>
                The RequestStatusBadge component can be displayed without tooltips by setting the
                showTooltip prop to false.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                {statuses.map((status) => (
                  <RequestStatusBadge
                    key={status}
                    status={status as any}
                    dictionary={dictionary}
                    showTooltip={false}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Usage example */}
        <section className="mb-10">
          <H2 className="mb-4">Usage Example</H2>
          <Card>
            <CardHeader>
              <CardTitle>In a Request List</CardTitle>
              <CardDescription>
                Example of how the RequestStatusBadge component can be used in a request list.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {statuses.map((status, index) => (
                  <div
                    key={status}
                    className="flex items-center justify-between p-4 border rounded-md"
                  >
                    <div>
                      <div className="font-medium">Request #{index + 1001}</div>
                      <div className="text-sm text-muted-foreground">
                        Created on {new Date().toLocaleDateString()}
                      </div>
                    </div>
                    <RequestStatusBadge status={status as any} dictionary={dictionary} size="md" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}
