"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  H1,
  H2,
  H3,
  H4,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Lead,
  PageTitle,
  SectionTitle,
  SubsectionTitle,
} from "@/components/typography";
import { PageLayout } from "@/components/layouts";

export default function GitHubWorkflowPage() {
  return (
    <div className="space-y-8">
      <PageLayout
        title="AI Assistant Guide to GitHub Workflow"
        description="A comprehensive guide to the GitHub workflow and methodology for AI assistants"
      >
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="methodology">Methodology</TabsTrigger>
            <TabsTrigger value="structure">Issue Structure</TabsTrigger>
            <TabsTrigger value="branching">Branching Strategy</TabsTrigger>
            <TabsTrigger value="pr-workflow">PR Workflow</TabsTrigger>
            <TabsTrigger value="scripts">Helper Scripts</TabsTrigger>
            <TabsTrigger value="plantuml">PlantUML WBS</TabsTrigger>
            <TabsTrigger value="ai-communication">AI Communication</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>GitHub Workflow Purpose and Goals</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <P>
                  <strong>
                    Note: This guide is specifically designed for AI assistants, not human
                    developers.
                  </strong>{" "}
                  It provides a structured reference for understanding and implementing the
                  project's GitHub workflow methodology.
                </P>

                <P>
                  This GitHub workflow is designed to provide a structured, hierarchical approach to
                  software development that maintains clear traceability from business requirements
                  to implementation. It follows a domain-driven design approach with a 3-level
                  hierarchy of issues.
                </P>

                <H3>Key Goals</H3>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Maintain clear traceability from business requirements to implementation</li>
                  <li>Organize work in a hierarchical structure for better management</li>
                  <li>Support parallel development with isolated feature branches</li>
                  <li>Ensure proper code review and quality control</li>
                  <li>Facilitate collaboration between humans and AI assistants</li>
                </ul>

                <H3>How to Use This Guide</H3>
                <P>
                  When asked to implement features or fix bugs, refer to this guide to understand
                  the proper workflow for creating branches, linking issues, and submitting pull
                  requests. The workflow follows specific patterns that should be consistently
                  applied to maintain the project's structure and traceability.
                </P>

                <P>
                  At the beginning of each session, the user will share this guide with you to
                  ensure you understand the project's workflow methodology. You should use the
                  PlantUML WBS tool to create a visual work breakdown structure for planning before
                  implementing any features.
                </P>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="methodology" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Development Methodology</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>Domain-Feature-Task Hierarchy</H3>
                <P>The project follows a 3-level hierarchical methodology for organizing work:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Domain (Epic):</strong> High-level business domain or major feature area
                    (e.g., Contact Management, Employee Management)
                  </li>
                  <li>
                    <strong>Feature:</strong> Specific feature within a domain (e.g., Contact
                    Creation, Employee List View)
                  </li>
                  <li>
                    <strong>Task:</strong> Individual implementation task for a specific feature
                    (e.g., Create Contact Form, Implement Contact Validation)
                  </li>
                </ul>

                <H3>Vertical Implementation Approach</H3>
                <P>
                  The project prefers a vertical implementation approach rather than a horizontal
                  one:
                </P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Vertical:</strong> Implement a complete feature end-to-end with a
                    minimal set of functionality, then iterate to expand
                  </li>
                  <li>
                    <strong>Horizontal:</strong> Implement all components of a feature at once
                    across all layers (UI, business logic, data)
                  </li>
                </ul>
                <P>
                  The vertical approach allows for quicker validation of the implementation approach
                  and better understanding of challenges before expanding to a full solution.
                </P>

                <H3>First Draft Isolation</H3>
                <P>
                  For new features or experimental implementations, create a separate domain to
                  avoid mixing concerns with other domain code until the approach is validated.
                </P>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="structure" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Issue Structure and Hierarchy</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>Issue Hierarchy</H3>
                <P>Issues follow a hierarchical structure with parent-child relationships:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Epic Issues:</strong> High-level domain or feature area with [EPIC]
                    prefix
                  </li>
                  <li>
                    <strong>Feature Issues:</strong> Specific features with [FEATURE] prefix
                  </li>
                  <li>
                    <strong>Task Issues:</strong> Individual implementation tasks with [TASK] prefix
                  </li>
                </ul>

                <H3>Issue Naming Convention</H3>
                <P>Issues should follow a consistent naming convention:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Epic:</strong> [EPIC] Domain Name (e.g., [EPIC] Contact Management
                    Domain)
                  </li>
                  <li>
                    <strong>Feature:</strong> [FEATURE] Feature Name (e.g., [FEATURE] Contact
                    Creation Form)
                  </li>
                  <li>
                    <strong>Task:</strong> [TASK] Task Name (e.g., [TASK] Implement Contact
                    Validation)
                  </li>
                </ul>

                <H3>Issue Content Structure</H3>
                <P>Issues should include the following sections:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Overview:</strong> Brief description of the issue
                  </li>
                  <li>
                    <strong>Business Value:</strong> Why this work is important
                  </li>
                  <li>
                    <strong>Requirements:</strong> What needs to be implemented
                  </li>
                  <li>
                    <strong>Acceptance Criteria:</strong> How to determine if the work is complete
                  </li>
                  <li>
                    <strong>Technical Notes:</strong> Implementation details and considerations
                  </li>
                  <li>
                    <strong>Related Issues:</strong> Links to parent or related issues
                  </li>
                </ul>

                <H3>Parent-Child Relationships</H3>
                <P>
                  Issues should be linked in a hierarchical structure using parent-child
                  relationships:
                </P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Epic issues are parents of Feature issues</li>
                  <li>Feature issues are parents of Task issues</li>
                  <li>
                    These relationships are established using the add-sub-issue.sh script (see
                    Scripts tab)
                  </li>
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="branching" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Branching Strategy</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>Branch Naming Convention</H3>
                <P>
                  Branches should follow a consistent naming convention based on the issue type and
                  number:
                </P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Epic branches:</strong> epic/issue-{"{number}"}-{"{short-description}"}
                    (e.g., epic/issue-53-contact-management-domain)
                  </li>
                  <li>
                    <strong>Feature branches:</strong> feature/issue-{"{number}"}-
                    {"{short-description}"}
                    (e.g., feature/issue-211-database-schema-workflow-management)
                  </li>
                  <li>
                    <strong>Task branches:</strong> feature/issue-{"{number}"}-
                    {"{short-description}"}
                    (e.g., feature/issue-216-create-automation-drafts-table)
                  </li>
                </ul>

                <H3>Branch Hierarchy</H3>
                <P>Branches should follow the same hierarchy as issues:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Epic branches are created from main</li>
                  <li>Feature branches are created from their parent Epic branch</li>
                  <li>Task branches are created from their parent Feature branch</li>
                </ul>

                <H3>Branch Creation Process</H3>
                <P>When implementing a task:</P>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Ensure the parent branch exists (create it if necessary)</li>
                  <li>
                    Create a new branch from the parent branch with the appropriate naming
                    convention
                  </li>
                  <li>Implement the task in this branch</li>
                  <li>Create a pull request to merge back into the parent branch</li>
                </ol>

                <H3>Branch Isolation</H3>
                <P>
                  Each branch should be focused on a single issue to maintain clean, reviewable pull
                  requests. Don't mix unrelated changes in the same branch.
                </P>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pr-workflow" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Pull Request Workflow</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>Bottom-Up PR Strategy</H3>
                <P>
                  Pull requests should follow a bottom-up approach, matching the issue hierarchy:
                </P>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Task branches are merged into their parent Feature branch</li>
                  <li>Feature branches are merged into their parent Epic branch</li>
                  <li>Epic branches are merged into main</li>
                </ol>

                <H3>PR Creation Process</H3>
                <P>When creating a pull request:</P>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Create a PR from your branch to its parent branch (not directly to main)</li>
                  <li>Include a descriptive title referencing the issue number</li>
                  <li>
                    Include a detailed description with:
                    <ul className="list-disc pl-6 mt-2">
                      <li>Overview of changes</li>
                      <li>List of implemented features</li>
                      <li>Testing performed</li>
                      <li>Related issues (using "Closes #X" syntax for automatic linking)</li>
                    </ul>
                  </li>
                  <li>Wait for review and approval before merging</li>
                </ol>

                <H3>PR Review Process</H3>
                <P>Pull requests should be reviewed for:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Code quality and adherence to project standards</li>
                  <li>Proper test coverage</li>
                  <li>Fulfillment of acceptance criteria</li>
                  <li>Security considerations</li>
                  <li>Performance implications</li>
                </ul>

                <H3>Merging Strategy</H3>
                <P>When all sub-issues for a parent issue are completed:</P>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Ensure all task PRs are merged into the feature branch</li>
                  <li>Create a PR from the feature branch to the epic branch</li>
                  <li>Once all feature PRs are merged, create a PR from the epic branch to main</li>
                </ol>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="scripts" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Helper Scripts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>add-sub-issue.sh Script</H3>
                <P>
                  The project includes a script to establish parent-child relationships between
                  issues:
                </P>
                <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                  {`./scripts/add-sub-issue.sh <parent_issue_number> <child_issue_number>`}
                </pre>
                <P>
                  This script uses the GitHub API to create a parent-child relationship between two
                  issues. It should be used whenever a new issue is created that is a sub-issue of
                  another issue.
                </P>

                <H3>Usage Example</H3>
                <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                  {`# Make issue #216 a sub-issue of issue #211
./scripts/add-sub-issue.sh 211 216

# Output:
Getting ID for issue #216...
Adding issue #216 (ID: 3060731491) as a sub-issue to issue #211
Success! Issue #216 is now a sub-issue of issue #211`}
                </pre>

                <H3>Other Useful Scripts</H3>
                <P>The project includes other helper scripts for common tasks:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <code>npm run db:reset</code> - Reset the database and apply migrations
                  </li>
                  <li>
                    <code>npm run test:rls</code> - Run Row Level Security tests
                  </li>
                  <li>
                    <code>npm run db:migration:new</code> - Create a new database migration
                  </li>
                  <li>
                    <code>npm run db:seed</code> - Seed the database with test data
                  </li>
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="plantuml" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>PlantUML Work Breakdown Structure</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>Using PlantUML for Work Breakdown Structure</H3>
                <P>
                  When starting a new feature implementation, AI assistants should use PlantUML to
                  create a visual Work Breakdown Structure (WBS) diagram to help plan and organize
                  the work. This provides a clear visual representation of the hierarchical
                  structure of tasks that can be reviewed before creating GitHub issues.
                </P>

                <H3>About @brainstack/plantuml-mcp</H3>
                <P>
                  The <code>@brainstack/plantuml-mcp</code> is a specialized tool for AI assistants
                  that renders PlantUML diagrams. It's a server that converts PlantUML code into
                  visual diagrams that can be shared with users.
                </P>

                <P>Key features of @brainstack/plantuml-mcp:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Renders PlantUML diagrams directly from code</li>
                  <li>Supports all PlantUML diagram types, including WBS</li>
                  <li>Generates images that can be shared with users</li>
                  <li>Integrates seamlessly with AI assistant workflows</li>
                  <li>
                    Available as an npm package: <code>npm install @brainstack/plantuml-mcp</code>
                  </li>
                </ul>

                <P>
                  For more information, visit:{" "}
                  <a
                    href="https://www.npmjs.com/package/@brainstack/plantuml-mcp"
                    className="text-blue-500 underline"
                  >
                    https://www.npmjs.com/package/@brainstack/plantuml-mcp
                  </a>
                </P>

                <H3>Process for Using PlantUML WBS</H3>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>
                    Use the <code>@brainstack/plantuml-mcp</code> tool to create a WBS diagram
                  </li>
                  <li>Present the diagram to the user for review and discussion</li>
                  <li>Iterate on the diagram based on feedback until approved</li>
                  <li>
                    Once approved, create GitHub issues following the hierarchy in the diagram
                  </li>
                  <li>
                    Link issues using the add-sub-issue.sh script to establish parent-child
                    relationships
                  </li>
                </ol>

                <H3>PlantUML WBS Syntax Reference</H3>
                <P>Here's the basic syntax for creating WBS diagrams in PlantUML:</P>
                <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                  {`@startwbs
* Epic Level (Domain)
** Feature Level 1
*** Task Level 1.1
*** Task Level 1.2
** Feature Level 2
*** Task Level 2.1
*** Task Level 2.2
@endwbs`}
                </pre>

                <H3>Example WBS for Workflow Engine</H3>
                <P>Here's an example WBS diagram for a workflow engine feature:</P>
                <pre className="bg-muted p-4 rounded-md text-xs overflow-auto">
                  {`@startwbs
* [#SkyBlue] Workflow Engine & Step Wizard
** [#LightBlue] Database Schema
*** [#LightGreen] Create Automation Drafts Table
*** [#LightGreen] Create Automation Executions Table
*** [#LightGreen] Create Automation Notifications Table
** [#LightBlue] Core Server Actions
*** [#LightGreen] Implement Save Draft Action
*** [#LightGreen] Implement Load Draft Action
*** [#LightGreen] Implement Complete Workflow Action
** [#LightBlue] Domain Structure
*** [#LightGreen] Create Folder Structure
*** [#LightGreen] Implement Landing Page
** [#LightBlue] Employee Creation Wizard
*** [#LightGreen] Create Wizard Entry Point
*** [#LightGreen] Implement Basic Info Step
*** [#LightGreen] Implement Review Step
@endwbs`}
                </pre>

                <H3>Advanced WBS Features</H3>
                <P>PlantUML WBS supports additional features:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <strong>Direction:</strong> Use <code>{"<"}</code> and <code>{">"}</code> to
                    change direction
                  </li>
                  <li>
                    <strong>Colors:</strong> Use <code>[#ColorName]</code> to set node colors
                  </li>
                  <li>
                    <strong>Styles:</strong> Use <code>{"<style>"}</code> to customize appearance
                  </li>
                  <li>
                    <strong>Box Removal:</strong> Use underscore <code>_</code> to remove boxes
                  </li>
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ai-communication" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Communication Guidelines</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <H3>Purpose of This Guide</H3>
                <P>
                  This guide is specifically designed for AI assistants, not human developers. It
                  provides a structured reference for understanding and implementing the project's
                  GitHub workflow methodology. By following these guidelines, AI assistants can
                  better assist with project management and implementation tasks.
                </P>

                <H3>Initial Session Workflow</H3>
                <P>When starting a new session with a user, follow these steps:</P>
                <ol className="list-decimal pl-6 space-y-2">
                  <li>Review this GitHub workflow guide to understand the project's methodology</li>
                  <li>
                    Use PlantUML to create a work breakdown structure for the requested feature
                  </li>
                  <li>Present the WBS diagram to the user for review and discussion</li>
                  <li>Iterate on the diagram until the user approves the structure</li>
                  <li>Create GitHub issues following the approved hierarchy</li>
                  <li>Link issues using the add-sub-issue.sh script</li>
                  <li>Begin implementation following the bottom-up approach</li>
                </ol>

                <H3>Communication Best Practices</H3>
                <P>When communicating about GitHub workflow:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Use consistent terminology (Epic, Feature, Task) when discussing issues</li>
                  <li>Reference issue numbers when discussing specific tasks</li>
                  <li>Clearly explain the parent-child relationships between issues</li>
                  <li>
                    Provide visual representations (like PlantUML diagrams) when planning work
                  </li>
                  <li>Explain the branching strategy when creating new branches</li>
                  <li>Clarify the PR workflow when submitting changes</li>
                </ul>

                <H3>Understanding the add-sub-issue.sh Script</H3>
                <P>
                  The <code>add-sub-issue.sh</code> script is a critical tool for establishing
                  parent-child relationships between GitHub issues. It uses the GitHub GraphQL API
                  to create these relationships, which are not directly available through the GitHub
                  UI.
                </P>

                <P>The script takes two parameters:</P>
                <ul className="list-disc pl-6 space-y-2">
                  <li>
                    <code>{"<parent_issue_number>"}</code>: The issue number of the parent issue
                  </li>
                  <li>
                    <code>{"<child_issue_number>"}</code>: The issue number of the child issue
                  </li>
                </ul>

                <P>
                  When creating a new issue that should be a sub-issue of another, always use this
                  script to establish the relationship. This ensures proper hierarchy and
                  traceability in the project.
                </P>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </PageLayout>
    </div>
  );
}
