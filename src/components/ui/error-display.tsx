"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, RefreshCw } from "lucide-react";
import { logger } from "@/lib/logger/services/LoggerService";
import { GoBackButton } from "./go-back-button";

interface ErrorDisplayProps {
  error: Error & { digest?: string };
  reset: () => void;
  title?: string;
  message?: string;
}

/**
 * Reusable error display component
 * Can be used in error.tsx files throughout the application
 */
export function ErrorDisplay({
  error,
  reset,
  title = "An error occurred", // Will be overridden by i18n in most cases
  message = "There was an error. Please try again.", // Will be overridden by i18n in most cases
}: ErrorDisplayProps) {
  // Log the error for debugging
  useEffect(() => {
    logger.error(`Error: ${error.message}`);
  }, [error]);

  return (
    <Card className="border-destructive">
      <CardHeader>
        <CardTitle className="flex items-center text-destructive">
          <AlertCircle className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">{message}</p>
        <p className="text-sm text-destructive mt-2">{error.message}</p>
        {error.digest && (
          <p className="text-xs text-muted-foreground mt-1">Error ID: {error.digest}</p>
        )}
      </CardContent>
      <CardFooter className="flex justify-between items-center gap-4">
        <GoBackButton label={title === "An error occurred" ? "Go Back" : "Back"} />
        <Button onClick={reset} className="flex items-center">
          <RefreshCw className="h-4 w-4 mr-2" />
          {title === "An error occurred" ? "Try Again" : "Retry"}
        </Button>
      </CardFooter>
    </Card>
  );
}
