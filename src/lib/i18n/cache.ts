import { cache } from "react";
import { i18n } from "./services/I18nService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { UserProfileService } from "@/app/[lang]/protected/user/lib/services/UserProfileService";
import { defaultLocale } from "./settings";
import { logger } from "@/lib/logger/services/LoggerService";
import type { Dictionary } from "./services/I18nService";
import { cookies, headers } from "next/headers";

// // Type declarations to fix TypeScript errors with Next.js headers and cookies
// declare module "next/headers" {
//   function headers(): Headers;
//   function cookies(): {
//     get(name: string): { value: string } | undefined;
//     set(name: string, value: string, options?: any): void;
//   };
// }

/**
 * Cached function that determines the appropriate language and returns the dictionary
 * This will only execute once per request, regardless of how many components call it
 */
export const getDictionary = cache(async (): Promise<Dictionary> => {
  // First check if we're in a protected route with an authenticated user
  try {
    const user = await auth.getCurrentUser();
    if (user) {
      // For authenticated users, prefer their profile setting
      const profile = await UserProfileService.getCurrentUserProfile();
      if (profile?.language) {
        logger.info(`Using language from user profile: ${profile.language}`);
        return i18n.getDictionary(profile.language);
      }
    }
  } catch (error) {
    // If auth check fails, continue to other methods
    logger.error("Error checking user language preference:", error as Error);
  }

  // Next, check URL parameter from headers if available
  try {
    const headersList = await headers();
    // Headers() returns a synchronous object in Next.js 15
    const pathname = headersList.get("x-pathname") || "";
    const langFromUrl = pathname.split("/")[1];

    if (i18n.isValidLocale(langFromUrl)) {
      logger.info(`Using language from URL: ${langFromUrl}`);
      return i18n.getDictionary(langFromUrl);
    }
  } catch (error) {
    // If header check fails, continue to other methods
    logger.error("Error checking URL language parameter:", error as Error);
  }

  // Next, check cookies
  try {
    const cookieStore = await cookies();
    // cookies() returns a synchronous object in Next.js 15
    const langFromCookie = cookieStore.get("preferred-language")?.value;

    if (langFromCookie && i18n.isValidLocale(langFromCookie)) {
      logger.info(`Using language from cookie: ${langFromCookie}`);
      return i18n.getDictionary(langFromCookie);
    }
  } catch (error) {
    // If cookie check fails, continue to other methods
    logger.error("Error checking cookie language preference:", error as Error);
  }

  // Finally, fall back to default locale
  logger.info(`Using default language: ${defaultLocale}`);
  return i18n.getDictionary(defaultLocale);
});

/**
 * Gets a domain-specific dictionary
 * @param domain Domain to get dictionary for
 * @returns The domain-specific dictionary
 */
export const getDomainDictionary = cache(
  async <K extends keyof Dictionary>(domain: K): Promise<Dictionary[K]> => {
    const dictionary = await getDictionary();
    return dictionary[domain];
  }
);

/**
 * Gets a domain and feature-specific dictionary
 * @param domain Domain to get dictionary for
 * @param feature Feature to get dictionary for
 * @returns The domain and feature-specific dictionary
 */
export const getDomainFeatureDictionary = cache(
  async <K extends keyof Dictionary, F extends keyof Dictionary[K]>(
    domain: K,
    feature: F
  ): Promise<Dictionary[K][F]> => {
    const dictionary = await getDictionary();
    return dictionary[domain][feature];
  }
);

/**
 * Updates the user's language preference
 * @param language The new language preference
 */
export async function updateLanguagePreference(language: string): Promise<void> {
  if (!i18n.isValidLocale(language)) {
    throw new Error(`Invalid language: ${language}`);
  }

  try {
    // Update in database for authenticated users
    const user = await auth.getCurrentUser();
    if (user) {
      await UserProfileService.updateSettings({
        language: language as "en" | "fr",
        notificationPreferences: { email: true, sms: true, inApp: true }, // Default values
      });
      logger.info(`Updated language preference in database: ${language}`);
    }

    // Also set in cookie for all users
    (await cookies()).set("preferred-language", language, {
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 365, // 1 year
    });

    logger.info(`Updated language preference in cookie: ${language}`);
  } catch (error) {
    logger.error(`Error updating language preference: ${error}`);
    throw error;
  }
}
