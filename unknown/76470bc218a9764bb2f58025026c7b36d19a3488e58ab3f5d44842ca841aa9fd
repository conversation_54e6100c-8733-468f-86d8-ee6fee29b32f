"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { FileEdit, Clock, CheckCircle2, XCircle, AlertCircle, ClipboardList } from "lucide-react";
import { <PERSON>lt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { RequestStatus } from "../lib/types";

interface RequestStatusBadgeProps {
  status: RequestStatus;
  dictionary?: any;
  showIcon?: boolean;
  showTooltip?: boolean;
  size?: "sm" | "md" | "lg";
  className?: string;
}

/**
 * RequestStatusBadge component
 * Displays a badge with the request status, with appropriate colors and icons
 *
 * @example
 * ```tsx
 * <RequestStatusBadge status="approved" dictionary={dictionary} />
 * ```
 */
export function RequestStatusBadge({
  status,
  dictionary,
  showIcon = true,
  showTooltip = true,
  size = "md",
  className,
}: RequestStatusBadgeProps) {
  // Get status label from dictionary or use fallback
  const getStatusLabel = () => {
    // First try to get from request.status dictionary path
    if (
      dictionary?.request?.status?.[status] &&
      typeof dictionary.request.status[status] === "string"
    ) {
      return dictionary.request.status[status];
    }

    // Then try the legacy statuses path
    if (dictionary?.statuses?.[status] && typeof dictionary.statuses[status] === "string") {
      return dictionary.statuses[status];
    }

    // Fallback labels if dictionary is not provided
    const fallbackLabels: Record<RequestStatus, string> = {
      draft: "Draft",
      processing: "Processing",
      waitlist: "Waitlist",
      completed: "Completed",
      closed: "Closed",
    };

    return fallbackLabels[status] || status;
  };

  // Get status description from dictionary or use fallback
  const getStatusDescription = () => {
    // First try to get from request.statusDescriptions dictionary path
    if (
      dictionary?.request?.statusDescriptions?.[status] &&
      typeof dictionary.request.statusDescriptions[status] === "string"
    ) {
      return dictionary.request.statusDescriptions[status];
    }

    // Then try the legacy statusDescriptions path
    if (
      dictionary?.statusDescriptions?.[status] &&
      typeof dictionary.statusDescriptions[status] === "string"
    ) {
      return dictionary.statusDescriptions[status];
    }

    // Fallback descriptions if dictionary is not provided
    const fallbackDescriptions: Record<RequestStatus, string> = {
      draft: "Initial state, request is being prepared and not yet submitted",
      processing: "Request has been submitted and is being processed according to business rules",
      waitlist: "Service cannot be provided immediately due to resource constraints",
      completed: "Service can be provided, case file has been initiated",
      closed: "Request has been manually closed and is no longer active",
    };

    return fallbackDescriptions[status] || "";
  };

  // Get status icon based on status
  const getStatusIcon = () => {
    switch (status) {
      case "draft":
        return <FileEdit className={iconSizeClass} />;
      case "processing":
        return <ClipboardList className={iconSizeClass} />;
      case "waitlist":
        return <Clock className={iconSizeClass} />;
      case "completed":
        return <CheckCircle2 className={iconSizeClass} />;
      case "closed":
        return <XCircle className={iconSizeClass} />;
      default:
        return <AlertCircle className={iconSizeClass} />;
    }
  };

  // Get badge variant based on status
  const getBadgeVariant = () => {
    switch (status) {
      case "draft":
        return "outline";
      case "processing":
        return "secondary";
      case "waitlist":
        return "secondary";
      case "completed":
        return "default";
      case "closed":
        return "outline";
      default:
        return "secondary";
    }
  };

  // Get custom class based on status
  const getCustomClass = () => {
    switch (status) {
      case "draft":
        return "border-slate-400 text-slate-700 dark:text-slate-300";
      case "processing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100";
      case "waitlist":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100";
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100";
      case "closed":
        return "border-gray-500 text-gray-700 dark:text-gray-300";
      default:
        return "";
    }
  };

  // Get size classes
  const getSizeClass = () => {
    switch (size) {
      case "sm":
        return "text-xs px-1.5 py-0";
      case "lg":
        return "text-sm px-3 py-1";
      case "md":
      default:
        return "text-xs px-2 py-0.5";
    }
  };

  // Icon size class based on badge size
  const iconSizeClass = size === "lg" ? "h-4 w-4" : "h-3 w-3";

  // Badge content
  const badgeContent = (
    <Badge
      variant={getBadgeVariant()}
      className={cn(getSizeClass(), getCustomClass(), "font-medium", className)}
    >
      {showIcon && <span className="mr-1">{getStatusIcon()}</span>}
      {getStatusLabel()}
    </Badge>
  );

  // Render with or without tooltip
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{badgeContent}</TooltipTrigger>
          <TooltipContent>
            <p>{getStatusDescription()}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badgeContent;
}
