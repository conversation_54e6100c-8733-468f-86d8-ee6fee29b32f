"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";
import {
  ActionState,
  errorActionState,
  successActionState,
} from "@/lib/types/responses/actionState";
import { createDraft } from "../../../actions/drafts/create";
import { EmployeeInsert } from "../types/employee";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { employeeWizardConfig } from "../config/employeeWizardConfigData";
import { Draft } from "../../../lib/types/draft";

/**
 * Create a new employee draft and redirect to the first step
 * @param prevState Previous state
 * @returns Result of the create operation
 */
export async function createEmployeeDraft(
  _prevState: ActionState<Draft>
): Promise<ActionState<Draft>> {
  // Flag to track if we should redirect
  let shouldRedirect = false;
  let result;

  try {
    const lang = (await auth.getCurrentUserProfile())?.language || "en";
    // Create initial empty employee data
    const initialData: Partial<EmployeeInsert> = {};

    // Create draft
    result = await createDraft<EmployeeInsert>(
      employeeWizardConfig.workflowType,
      initialData as EmployeeInsert
    );

    if (!result.success) {
      logger.error(`Error creating draft: ${result.error}`);
      return errorActionState(`Failed to create draft: ${result.error}`);
    }

    // Revalidate path
    revalidatePath(`/${lang}/protected/automation`, "page");

    // Set redirect flag
    shouldRedirect = true;

    // Return success state (this will only be used if redirect doesn't happen)
    return successActionState(result.data as Draft);
  } catch (error) {
    logger.error(`Unexpected error creating employee draft: ${error}`);
    return errorActionState("An unexpected error occurred while creating the employee draft");
  } finally {
    // Redirect outside of try-catch if successful
    if (shouldRedirect && result && result.data) {
      const lang = (await auth.getCurrentUserProfile())?.language || "en";
      const draftId = result.data.id;
      redirect(`/${lang}/protected/automation/wizard/${draftId}/basic_info`);
    }
  }
}
