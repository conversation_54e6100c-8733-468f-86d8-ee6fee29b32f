import { WizardConfig, WizardStep } from "../../../lib/types";

// Import step components - these will be created later
// For now, we'll use placeholder imports that we'll implement in Phase 2
import BasicInfoStep from "../components/steps/BasicInfoStep";
import RelatedContactsStep from "../components/steps/RelatedContactsStep";
import ServiceRequirementsStep from "../components/steps/ServiceRequirementsStep";
import FamilyAvailabilityStep from "../components/steps/FamilyAvailabilityStep";
import ReviewStep from "../components/steps/ReviewStep";

/**
 * Request wizard configuration
 *
 * Note: The labels here are just for internal reference.
 * The actual displayed labels come from the i18n dictionary.
 */
export const requestWizardConfig: WizardConfig = {
  workflowType: "request_creation",
  title: "Create Request", // This will be translated in the UI
  description: "Complete the following steps to create a new request", // This will be translated in the UI
  initialStep: "basic_info",
  stepsMap: new Map<string, WizardStep>([
    [
      "basic_info",
      {
        id: "basic_info",
        label: "Basic Info", // This will be translated in the UI
        nextStepId: "related_contacts",
        component: BasicInfoStep,
      },
    ],
    [
      "related_contacts",
      {
        id: "related_contacts",
        label: "Related Contacts", // This will be translated in the UI
        nextStepId: "service_requirements",
        previousStepId: "basic_info",
        component: RelatedContactsStep,
      },
    ],
    [
      "service_requirements",
      {
        id: "service_requirements",
        label: "Service Requirements", // This will be translated in the UI
        nextStepId: "family_availability",
        previousStepId: "related_contacts",
        component: ServiceRequirementsStep,
      },
    ],
    [
      "family_availability",
      {
        id: "family_availability",
        label: "Family Availability", // This will be translated in the UI
        nextStepId: "review",
        previousStepId: "service_requirements",
        component: FamilyAvailabilityStep,
      },
    ],
    [
      "review",
      {
        id: "review",
        label: "Review", // This will be translated in the UI
        previousStepId: "family_availability",
        isFinalStep: true,
        component: ReviewStep,
      },
    ],
  ]),
};
