import { DraftService } from "../../../../lib/services/DraftService";
import { getWizardConfig } from "../../../../lib/config/wizardRegistry";
import { WizardProgress } from "../../components";
import WizardNavigation from "../../components/WizardNavigation";
import { i18n } from "@/lib/i18n/services/I18nService";

interface WizardLayoutProps {
  params: Promise<{
    draftId: string;
    lang: string;
  }>;
  children: React.ReactNode;
}

/**
 * Wizard layout component
 * Provides the shell for the wizard with progress indicator and form wrapper
 */
export default async function WizardLayout({ params, children }: WizardLayoutProps) {
  const resolvedParams = await params;
  const { draftId, lang } = resolvedParams;
  const draftResult = await DraftService.getDraft(draftId);

  if (!draftResult.success) {
    throw new Error(`Failed to get draft: ${draftResult.message}`);
  }

  const draft = draftResult.data;

  if (!draft) {
    throw new Error("Draft data is null");
  }

  const currentStep = draft.current_step;

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);
  const requestDictionary = dictionary.automation["request-wizard"];

  // Create translated labels for steps
  const translatedStepLabels: Record<string, string> = {};

  // Create button labels
  const buttonLabels = {
    previous: dictionary.common.previous,
    next: dictionary.common.next,
    submit: dictionary.common.submit,
    submitting: dictionary.common.submitting,
  };

  // Determine current step index and if it's the last step
  const steps = [...wizardConfig.stepsMap.values()];
  const currentStepIndex = steps.findIndex((step) => step.id === currentStep);
  const step = steps[currentStepIndex];
  const isLastStep = step?.isFinalStep ?? false;

  // Populate translated step labels
  steps.forEach((step) => {
    const stepId = step.id as keyof typeof requestDictionary.steps;
    if (requestDictionary.steps && requestDictionary.steps[stepId]) {
      translatedStepLabels[step.id] = requestDictionary.steps[stepId];
    }
  });

  return (
    <>
      {/* Progress indicator */}
      <WizardProgress
        wizardConfig={wizardConfig}
        currentStep={currentStep}
        draftId={draftId}
        translatedLabels={translatedStepLabels}
      />

      {/* Form wrapper */}
      <form id="wizard-form">
        <div className="min-h-[300px]">{children}</div>

        {/* Navigation */}
        <WizardNavigation
          draftId={draftId}
          step={step}
          currentStep={currentStepIndex}
          isLastStep={isLastStep}
          buttonLabels={buttonLabels}
        />
      </form>
    </>
  );
}
