{"name": "Execution Status Manager", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [0, 300], "typeVersion": 1}, {"parameters": {"options": {"dotNotation": true}}, "id": "node_2", "name": "Extract Parameters", "type": "n8n-nodes-base.set", "position": [200, 300], "typeVersion": 1}, {"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.executionId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "={{ $json.status }}"}, {"fieldId": "n8n_execution_id", "fieldValue": "={{ $json.n8nExecutionId }}"}, {"fieldId": "updated_at", "fieldValue": "={{ $now.toISOString() }}"}, {"fieldId": "error", "fieldValue": "={{ $json.error }}"}, {"fieldId": "result", "fieldValue": "={{ $json.result }}"}]}}, "id": "node_3", "name": "Update Execution Status", "type": "n8n-nodes-base.supabase", "position": [400, 300], "typeVersion": 1}, {"parameters": {}, "id": "node_4", "name": "Done", "type": "n8n-nodes-base.noOp", "position": [600, 300], "typeVersion": 1}], "pinData": {}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "Extract Parameters", "type": "main", "index": 0}]]}, "Extract Parameters": {"main": [[{"node": "Update Execution Status", "type": "main", "index": 0}]]}, "Update Execution Status": {"main": [[{"node": "Done", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "9177d3f0-e5f0-4f9b-9d47-430760339140", "meta": {"instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "tN4tz5pGHwCDiI1Q", "tags": []}