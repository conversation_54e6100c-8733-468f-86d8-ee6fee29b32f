{"name": "Request Creation V2 (Fixed Set)", "nodes": [{"parameters": {"inputSource": "jsonExample"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-240, 240], "id": "008dba70-112d-47c3-bbdc-75463cde207e", "name": "When Executed by Another Workflow"}, {"parameters": {"mode": "jsonExtended", "jsonParameters": true, "options": {}, "setAllData": false, "dataPropertyName": "data", "value": {"requestData": "={{\n  const payload = $input.item.json;\n  \n  // Default values if payload is invalid\n  if (!payload || !payload.body) {\n    return {\n      organization_id: payload?.organization_id || '',\n      requester_id: payload?.user_id || '',\n      status: 'processing',\n      location_id: null,\n      service_id: null,\n      start_date: null,\n      end_date: null,\n      duration: null,\n      periodicity: null,\n      frequency_count: null\n    };\n  }\n  \n  // Create the formatted request object\n  return {\n    organization_id: payload.body.organization_id,\n    requester_id: payload.body.user_id,\n    status: 'processing',\n    location_id: payload.body.data?.location_id || null,\n    service_id: payload.body.data?.service_id || null,\n    start_date: payload.body.data?.start_date || null,\n    end_date: payload.body.data?.end_date || null,\n    duration: payload.body.data?.duration || null,\n    periodicity: payload.body.data?.periodicity || null,\n    frequency_count: payload.body.data?.frequency_count || null\n  };\n}}", "metadataData": "={{\n  const payload = $input.item.json;\n  \n  // Default empty object if payload is invalid\n  if (!payload || !payload.body) {\n    return {};\n  }\n  \n  // Parse contact availability if it exists\n  let contactAvailability = null;\n  if (payload.body.data?.contact_availability) {\n    try {\n      if (typeof payload.body.data.contact_availability === 'string') {\n        contactAvailability = JSON.parse(payload.body.data.contact_availability);\n      } else {\n        contactAvailability = payload.body.data.contact_availability;\n      }\n    } catch (e) {\n      contactAvailability = payload.body.data.contact_availability;\n    }\n  }\n  \n  // Return the metadata object\n  return {\n    family_availability: contactAvailability\n  };\n}}", "relatedContacts": "={{\n  const payload = $input.item.json;\n  \n  // Default empty array if payload is invalid\n  if (!payload || !payload.body) {\n    return [];\n  }\n  \n  // Return related contacts or empty array\n  return payload.body.data?.relatedContacts || [];\n}}", "error": "={{!$input.item.json || !$input.item.json.body ? 'Invalid payload structure' : undefined}}"}}, "id": "4863ac6d-bb57-4960-9264-477a2e97f909", "name": "Prepare Request Data", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-20, 240]}, {"parameters": {"tableId": "requests", "dataToSend": "defineBelow", "columns": {"parameters": [{"column": "organization_id", "value": "={{ $json.requestData.organization_id }}"}, {"column": "requester_id", "value": "={{ $json.requestData.requester_id }}"}, {"column": "status", "value": "={{ $json.requestData.status }}"}, {"column": "location_id", "value": "={{ $json.requestData.location_id }}"}, {"column": "service_id", "value": "={{ $json.requestData.service_id }}"}, {"column": "start_date", "value": "={{ $json.requestData.start_date }}"}, {"column": "end_date", "value": "={{ $json.requestData.end_date }}"}, {"column": "duration", "value": "={{ $json.requestData.duration }}"}, {"column": "periodicity", "value": "={{ $json.requestData.periodicity }}"}, {"column": "frequency_count", "value": "={{ $json.requestData.frequency_count }}"}]}}, "id": "7e077c35-8c24-4e67-9c3a-8eaf83ff6aa0", "name": "Create Request Record", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [200, 240], "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"mode": "jsonExtended", "jsonParameters": true, "options": {}, "setAllData": false, "dataPropertyName": "data", "value": {"request_id": "={{ $('Create Request Record').item.json[0].id }}", "family_availability": "={{ $('Prepare Request Data').item.json.metadataData.family_availability }}"}}, "id": "548148c5-9628-48e8-812c-039cabd92793", "name": "Create Request Metadata", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [420, 240]}, {"parameters": {"tableId": "request_metadata", "dataToSend": "defineBelow", "columns": {"parameters": [{"column": "request_id", "value": "={{ $json.request_id }}"}, {"column": "family_availability", "value": "={{ $json.family_availability }}"}]}}, "id": "fd45a533-fbae-4680-bb65-df0009fbcb6a", "name": "Create Metadata Record", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [640, 240], "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"mode": "jsonExtended", "jsonParameters": true, "options": {}, "setAllData": false, "dataPropertyName": "data", "value": {"requestId": "={{ $('Create Request Record').item.json[0].id }}", "relatedContacts": "={{ $('Prepare Request Data').item.json.relatedContacts }}", "message": "={{ $('Prepare Request Data').item.json.relatedContacts.length ? `Found ${$('Prepare Request Data').item.json.relatedContacts.length} related contacts to process` : 'No related contacts to process' }}"}}, "id": "2feb9f79-f797-4ea8-b900-4ddb10ccfe28", "name": "Extract Related Contacts", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [860, 240]}, {"parameters": {"workflowId": {"__rl": true, "value": "VxQp3vj0csRvU0iv", "mode": "list", "cachedResultName": "Process Request Related Contacts"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": false}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1080, 240], "id": "b7a91a1e-09eb-4ed2-946f-8145e083557c", "name": "Process Related Contacts"}, {"parameters": {"mode": "jsonExtended", "jsonParameters": true, "options": {}, "setAllData": false, "dataPropertyName": "data", "value": {"success": true, "message": "Request created successfully", "result": {"id": "={{ $('Create Request Record').item.json[0].id }}", "status": "processing", "reference_number": "={{ $('Create Request Record').item.json[0].reference_number || 'N/A' }}"}, "notification": {"title": "Request Created", "message": "={{ `Request has been created successfully with reference number ${$('Create Request Record').item.json[0].reference_number || 'N/A'}` }}"}}}, "id": "c12d830c-e412-4e41-8edb-f6b03ef868ad", "name": "Success Response", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [1300, 240]}, {"parameters": {}, "type": "n8n-nodes-base.errorTrigger", "typeVersion": 1, "position": [-220, 480], "id": "71fadc46-726c-488f-94dd-c8970ca58474", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"mode": "jsonExtended", "jsonParameters": true, "options": {}, "setAllData": false, "dataPropertyName": "data", "value": {"success": false, "message": "={{ $input.all()[0].json.message || 'An error occurred while creating the request' }}", "result": {"error": "={{ $input.all()[0].json.message || 'An error occurred while creating the request' }}"}, "notification": {"title": "Request Creation Failed", "message": "={{ $input.all()[0].json.message || 'An error occurred while creating the request' }}"}}}, "id": "795be2ca-611f-4150-aaeb-507ca54c27ee", "name": "Error Response", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [0, 480]}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Prepare Request Data", "type": "main", "index": 0}]]}, "Prepare Request Data": {"main": [[{"node": "Create Request Record", "type": "main", "index": 0}]]}, "Create Request Record": {"main": [[{"node": "Create Request Metadata", "type": "main", "index": 0}]]}, "Create Request Metadata": {"main": [[{"node": "Create Metadata Record", "type": "main", "index": 0}]]}, "Create Metadata Record": {"main": [[{"node": "Extract Related Contacts", "type": "main", "index": 0}]]}, "Extract Related Contacts": {"main": [[{"node": "Process Related Contacts", "type": "main", "index": 0}]]}, "Process Related Contacts": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}, "Error Trigger": {"main": [[{"node": "Error Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}, "tags": [{"name": "Business Process"}]}