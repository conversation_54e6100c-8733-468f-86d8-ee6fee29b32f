"use client";

import { useState } from "react";
import { TimeSlot } from "@/components/ui/scheduling";
import { FamilyAvailabilityGrid } from "./FamilyAvailabilityGrid";
import { Draft } from "../../../../lib/types";

interface FamilyAvailabilityClientProps {
  draft: Draft<any>;
  selectedContactId: string | null;
  initialAvailability: Record<string, TimeSlot[]>;
  relatedContacts: any[];
  dictionary: any;
}

export function FamilyAvailabilityClient({
  draft: _draft,
  selectedContactId,
  initialAvailability,
  relatedContacts,
  dictionary,
}: FamilyAvailabilityClientProps) {
  // State to track availability changes
  const [availabilityData, setAvailabilityData] =
    useState<Record<string, TimeSlot[]>>(initialAvailability);

  // Handle availability changes from the grid
  const handleAvailabilityChange = (newAvailability: Record<string, TimeSlot[]>) => {
    setAvailabilityData(newAvailability);
  };

  // Find the selected contact object
  const selectedContact = selectedContactId
    ? relatedContacts.find((c) => c.id === selectedContactId)
    : null;

  return (
    <>
      {/* Hidden input to store availability data for form submission */}
      <input type="hidden" name="contact_availability" value={JSON.stringify(availabilityData)} />

      {selectedContactId && selectedContact ? (
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-3">
          <div className="flex justify-between items-center mb-3">
            <div>
              <h4 className="text-sm font-medium">
                {dictionary?.family_availability?.weeklyAvailability || "Weekly Availability"}
                <span className="ml-2 text-xs text-gray-400">{selectedContact?.name}</span>
              </h4>
              <div className="flex items-center">
                <p className="text-xs text-gray-400">
                  {dictionary?.family_availability?.clickToMark ||
                    "Click on time slots to mark as available"}
                </p>
                <div className="ml-2 text-xs text-primary/80">
                  •{" "}
                  {dictionary?.family_availability?.changesAutoSaved ||
                    "Changes will be saved when you click Next"}
                </div>
              </div>
            </div>
            <div>
              <button
                type="button"
                className="h-7 text-xs bg-transparent border border-gray-700 px-3 py-1 rounded-md hover:bg-gray-800"
                onClick={() => {
                  // Reset availability for this contact
                  const newAvailability = { ...availabilityData };
                  newAvailability[selectedContactId] = [];
                  setAvailabilityData(newAvailability);
                }}
              >
                Reset
              </button>
            </div>
          </div>

          <FamilyAvailabilityGrid
            initialTimeSlots={availabilityData[selectedContactId] || []}
            selectedContactId={selectedContactId}
            allAvailability={availabilityData}
            onAvailabilityChange={handleAvailabilityChange}
          />
        </div>
      ) : (
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-6 flex items-center justify-center h-full">
          <p className="text-gray-400 text-center">
            {dictionary?.family_availability?.selectToSet ||
              "Select a contact to set their availability"}
          </p>
        </div>
      )}
    </>
  );
}
