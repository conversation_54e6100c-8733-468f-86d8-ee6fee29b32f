"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { dtoDraftData } from "../../lib/dto/dtoDraftData";
import { updateDraft } from "../drafts/update";
import { getWizardConfig } from "../../lib/config/wizardRegistry";
import { handleNavigationError, redirectToStep } from "./navigationUtils";

/**
 * Complete the wizard
 * @param draftId The draft ID
 * @param formData The form data
 */
export async function completeStep(draftId: string, formData: FormData) {
  try {
    // Process draft data
    const { draft, updatedData } = await dtoDraftData(draftId, formData);

    // Get wizard configuration
    const wizardConfig = getWizardConfig(draft.workflow_type);
    const currentStep = wizardConfig.stepsMap.get(draft.current_step);

    if (!currentStep) {
      throw new Error(`Step not found in configuration: ${draft.current_step}`);
    }

    if (!currentStep.isFinalStep) {
      logger.warn(`Attempting to complete a non-final step: ${draft.current_step}`);
    }

    // Update draft
    await updateDraft(draftId, updatedData, "complete");
  } catch (error) {
    handleNavigationError("complete wizard", error, `/en/protected/automation/wizard/${draftId}`);
  }

  // Redirect to completion page
  redirectToStep(draftId, "complete");
}
