"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { H1 } from "@/components/typography";
import { RequestStatusBadge } from "./RequestStatusBadge";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Pencil, Calendar, Clock, User, FileText, Tag, Mail, Phone, MapPin } from "lucide-react";
import Link from "next/link";
// Helper function to format dates in a human-friendly way based on language
const formatDate = (dateString: string, lang: string): string => {
  try {
    const date = new Date(dateString);

    // Use the user's language for date formatting
    const locale = lang === "fr" ? "fr-CA" : "en-US";

    // Format based on locale with a more human-friendly format
    return new Intl.DateTimeFormat(locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: lang !== "fr", // French typically uses 24-hour format
    }).format(date);
  } catch (error) {
    return dateString;
  }
};
import { RequestWithRelations } from "../lib/types";
import DOMAIN_CONFIG from "../lib/config/domain";

interface RequestDetailProps {
  request: RequestWithRelations;
  dictionary: any;
  lang: string;
  canEdit?: boolean;
  simplified?: boolean;
}

/**
 * RequestDetail component
 * Displays all request properties in organized sections
 */
export function RequestDetail({ request, dictionary, lang, canEdit = true }: RequestDetailProps) {
  // Format dates with language-specific formatting
  const createdAt = request.created_at ? formatDate(request.created_at, lang) : "-";
  const updatedAt = request.updated_at ? formatDate(request.updated_at, lang) : "-";

  // Get service type and priority labels
  const getServiceTypeLabel = () => {
    // Service type is now stored in the service name
    const serviceType = request.service?.name || "";
    if (dictionary?.serviceTypes && serviceType && dictionary.serviceTypes[serviceType]) {
      // Check if the value is a string, otherwise use the key
      const value = dictionary.serviceTypes[serviceType];
      return typeof value === "string" ? value : serviceType.replace(/_/g, " ");
    }
    return serviceType.replace(/_/g, " ") || "-";
  };

  return (
    <div className="space-y-6">
      {/* Service Overview */}
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">
            {dictionary?.serviceOverview || "Service Overview"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Service Type */}
              <div>
                <h4 className="text-sm font-medium text-muted-foreground mb-2">
                  {dictionary?.serviceType || "Service Type"}
                </h4>
                <div className="flex items-center">
                  <Tag className="mr-2 h-4 w-4 text-primary" />
                  <span className="font-medium">{getServiceTypeLabel()}</span>
                </div>
              </div>

              {/* Location with all details */}
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium mb-3">{dictionary?.location || "Location"}</h4>

                <div className="space-y-3">
                  <div className="flex items-start">
                    <MapPin className="mr-2 h-4 w-4 text-primary mt-0.5" />
                    <div>
                      <div className="font-medium">
                        {request.location?.name ||
                          request.metadata?.location ||
                          dictionary?.noLocationSpecified ||
                          "No location specified"}
                      </div>
                      {request.location?.address && (
                        <div className="text-sm text-muted-foreground mt-1">
                          {request.location.address}
                        </div>
                      )}
                    </div>
                  </div>

                  {request.location?.emails && (
                    <div className="flex items-start ml-6">
                      <Mail className="mr-2 h-4 w-4 text-muted-foreground mt-0.5" />
                      <div>
                        {typeof request.location.emails === "string" ? (
                          <span className="text-sm">{request.location.emails}</span>
                        ) : (
                          <div className="space-y-1">
                            {typeof request.location.emails === "object" &&
                              Object.entries(request.location.emails as Record<string, string>).map(
                                ([_, emailValue], i) => (
                                  <div key={i} className="text-sm">
                                    {emailValue}
                                  </div>
                                )
                              )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {request.location?.phones && (
                    <div className="flex items-start ml-6">
                      <Phone className="mr-2 h-4 w-4 text-muted-foreground mt-0.5" />
                      <div>
                        {typeof request.location.phones === "string" ? (
                          <span className="text-sm">{request.location.phones}</span>
                        ) : (
                          <div className="space-y-1">
                            {typeof request.location.phones === "object" &&
                              Object.entries(request.location.phones as Record<string, string>).map(
                                ([_, phoneValue], i) => (
                                  <div key={i} className="text-sm">
                                    {phoneValue}
                                  </div>
                                )
                              )}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Schedule Information */}
              <div>
                <h4 className="text-sm font-medium mb-3">
                  {dictionary?.scheduleInformation || "Schedule Information"}
                </h4>

                <div className="space-y-4">
                  {/* Date Range */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-primary" />
                      <span className="font-medium">{dictionary?.dateRange || "Date Range"}</span>
                    </div>
                    <div className="ml-6 space-y-1">
                      <div className="flex items-center">
                        <span className="text-sm text-muted-foreground w-16">
                          {dictionary?.startDate || "Start"}:
                        </span>
                        <span className="text-sm">
                          {request.start_date
                            ? formatDate(request.start_date, lang)
                            : dictionary?.notSpecified || "Not specified"}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-sm text-muted-foreground w-16">
                          {dictionary?.endDate || "End"}:
                        </span>
                        <span className="text-sm">
                          {request.end_date
                            ? formatDate(request.end_date, lang)
                            : dictionary?.notSpecified || "Not specified"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Frequency */}
                  {request.metadata && request.metadata.service_requirements && (
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <Clock className="mr-2 h-4 w-4 text-primary" />
                        <span className="font-medium">{dictionary?.frequency || "Frequency"}</span>
                      </div>
                      <div className="ml-6 space-y-1">
                        <div className="flex items-center">
                          <span className="text-sm text-muted-foreground w-16">
                            {dictionary?.periodicity || "Period"}:
                          </span>
                          <span className="text-sm">
                            {request.metadata.service_requirements.frequency
                              ? (() => {
                                  const value =
                                    dictionary?.periodicities?.[
                                      request.metadata.service_requirements.frequency
                                    ];
                                  return typeof value === "string"
                                    ? value
                                    : request.metadata.service_requirements.frequency;
                                })()
                              : dictionary?.notSpecified || "Not specified"}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <span className="text-sm text-muted-foreground w-16">
                            {dictionary?.count || "Count"}:
                          </span>
                          <span className="text-sm">
                            {request.metadata.service_requirements.duration
                              ? `${request.metadata.service_requirements.duration} ${dictionary?.timesPerPeriod || "times per period"}`
                              : dictionary?.notSpecified || "Not specified"}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{dictionary?.contacts || "Contacts"}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {request.request_contacts && request.request_contacts.length > 0 ? (
            request.request_contacts.map((contactRel, index) => {
              const contact = contactRel.contact;
              if (!contact) return null;

              return (
                <div key={index} className="border-b pb-5 last:border-b-0 last:pb-0">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <User className="h-5 w-5 text-primary mr-2" />
                      <h3 className="font-medium text-lg">{contact.name || "Contact"}</h3>
                    </div>
                    {contactRel.relationship_type && (
                      <Badge variant="outline" className="text-xs">
                        {contactRel.relationship_type.replace(/_/g, " ")}
                      </Badge>
                    )}
                  </div>

                  <div className="space-y-3 ml-7">
                    {/* Contact Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {/* Email */}
                      {contact.email && (
                        <div className="flex items-start">
                          <Mail className="mr-2 h-4 w-4 text-muted-foreground mt-0.5" />
                          <div>
                            {typeof contact.email === "string" ? (
                              <span className="text-sm">{contact.email}</span>
                            ) : (
                              <div className="space-y-1">
                                {typeof contact.email === "object" &&
                                  Object.entries(contact.email as Record<string, string>).map(
                                    ([_, emailValue], i) => (
                                      <div key={i} className="text-sm">
                                        {emailValue}
                                      </div>
                                    )
                                  )}
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Phone */}
                      {contact.phone && (
                        <div className="flex items-start">
                          <Phone className="mr-2 h-4 w-4 text-muted-foreground mt-0.5" />
                          <div>
                            {typeof contact.phone === "string" ? (
                              <span className="text-sm">{contact.phone}</span>
                            ) : (
                              <div className="space-y-1">
                                {typeof contact.phone === "object" &&
                                  Object.entries(contact.phone as Record<string, string>).map(
                                    ([_, phoneValue], i) => (
                                      <div key={i} className="text-sm">
                                        {phoneValue}
                                      </div>
                                    )
                                  )}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Contact Availability */}
                    {request.metadata?.family_availability &&
                      typeof request.metadata.family_availability === "object" &&
                      !Array.isArray(request.metadata.family_availability) &&
                      (request.metadata.family_availability as Record<string, any>)[contact.id] && (
                        <div className="mt-2">
                          <div className="flex items-center mb-2">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            <span className="font-medium text-sm">
                              {dictionary?.availability || "Availability"}
                            </span>
                          </div>

                          {(() => {
                            const availability = (
                              request.metadata.family_availability as Record<string, any>
                            )[contact.id];
                            if (!Array.isArray(availability) || availability.length === 0) {
                              return (
                                <div className="text-xs text-muted-foreground italic ml-6">
                                  {dictionary?.noAvailabilitySpecified ||
                                    "No availability specified"}
                                </div>
                              );
                            }

                            // Group availability by day
                            const dayNames = [
                              "Sunday",
                              "Monday",
                              "Tuesday",
                              "Wednesday",
                              "Thursday",
                              "Friday",
                              "Saturday",
                            ];
                            const availabilityByDay: Record<number, number[]> = {};

                            // Initialize empty arrays for each day
                            dayNames.forEach((_, index) => {
                              availabilityByDay[index] = [];
                            });

                            // Group available hours by day
                            availability.forEach((slot) => {
                              if (
                                slot.available &&
                                slot.day >= 0 &&
                                slot.day <= 6 &&
                                slot.hour >= 0 &&
                                slot.hour < 24
                              ) {
                                availabilityByDay[slot.day].push(slot.hour);
                              }
                            });

                            // Convert hours to time ranges
                            const timeRangesByDay: Record<
                              number,
                              Array<{ start: number; end: number }>
                            > = {};
                            Object.keys(availabilityByDay).forEach((dayIndexStr) => {
                              const dayIndex = Number(dayIndexStr);
                              const hours = availabilityByDay[dayIndex].sort((a, b) => a - b);
                              const ranges: Array<{ start: number; end: number }> = [];

                              if (hours.length > 0) {
                                let rangeStart = hours[0];
                                let rangeEnd = hours[0];

                                for (let i = 1; i < hours.length; i++) {
                                  if (hours[i] === rangeEnd + 1) {
                                    // Continue the current range
                                    rangeEnd = hours[i];
                                  } else {
                                    // End the current range and start a new one
                                    ranges.push({ start: rangeStart, end: rangeEnd + 1 }); // +1 because end is exclusive
                                    rangeStart = hours[i];
                                    rangeEnd = hours[i];
                                  }
                                }

                                // Add the last range
                                ranges.push({ start: rangeStart, end: rangeEnd + 1 });
                              }

                              timeRangesByDay[dayIndex] = ranges;
                            });

                            // Format time (12-hour format with AM/PM)
                            const formatTime = (hour: number): string => {
                              const period = hour < 12 ? "AM" : "PM";
                              const displayHour = hour % 12 === 0 ? 12 : hour % 12;
                              return `${displayHour}:00 ${period}`;
                            };

                            // Check if there's any availability
                            const hasAvailability = Object.values(timeRangesByDay).some(
                              (ranges) => ranges.length > 0
                            );

                            if (!hasAvailability) {
                              return (
                                <div className="text-xs text-muted-foreground italic ml-6">
                                  {dictionary?.noAvailabilitySpecified ||
                                    "No availability specified"}
                                </div>
                              );
                            }

                            return (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 ml-6">
                                {dayNames
                                  .map((dayName, dayIndex) => {
                                    const ranges = timeRangesByDay[dayIndex] || [];

                                    if (ranges.length === 0) {
                                      return null; // Skip days with no availability
                                    }

                                    return (
                                      <div key={dayIndex} className="text-sm">
                                        <span className="font-medium">{dayName}: </span>
                                        <span className="text-muted-foreground">
                                          {ranges.map((range, rangeIndex) => (
                                            <span key={rangeIndex}>
                                              {formatTime(range.start)} - {formatTime(range.end)}
                                              {rangeIndex < ranges.length - 1 ? ", " : ""}
                                            </span>
                                          ))}
                                        </span>
                                      </div>
                                    );
                                  })
                                  .filter(Boolean)}
                              </div>
                            );
                          })()}
                        </div>
                      )}
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-muted-foreground italic">
              {dictionary?.noContactsAvailable || "No contacts available"}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Status Information */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">{dictionary?.requestStatus || "Request Status"}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-5">
            {/* Current Status */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">
                {dictionary?.currentStatus || "Current Status"}
              </h4>
              <div className="flex items-center">
                <RequestStatusBadge status={request.status} dictionary={dictionary} showTooltip />
                <span className="ml-3 text-sm text-muted-foreground">
                  {dictionary?.lastUpdated || "Last updated"}: {updatedAt}
                </span>
              </div>
            </div>

            {/* Request Information */}
            <div className="pt-4 border-t">
              <h4 className="text-sm font-medium mb-3">
                {dictionary?.requestInformation || "Request Information"}
              </h4>

              <div className="space-y-3">
                {/* Created by */}
                <div className="flex items-start">
                  <User className="mr-2 h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <div className="text-sm font-medium">
                      {dictionary?.requestedBy || "Requested by"}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {request.requester
                        ? request.requester.name || request.requester.email
                        : dictionary?.unknown || "Unknown"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {dictionary?.createdOn || "Created on"}: {createdAt}
                    </div>
                  </div>
                </div>

                {/* Assigned to */}
                {request.assignee && (
                  <div className="flex items-start">
                    <User className="mr-2 h-4 w-4 text-primary mt-0.5" />
                    <div>
                      <div className="text-sm font-medium">
                        {dictionary?.assignedTo || "Assigned to"}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {request.assignee.name || request.assignee.email}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
