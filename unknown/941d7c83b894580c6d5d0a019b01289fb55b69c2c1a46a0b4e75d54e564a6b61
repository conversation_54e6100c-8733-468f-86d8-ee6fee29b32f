import { ReactNode } from "react";
import { i18n } from "@/lib/i18n/services/I18nService";
import { PageTitle } from "@/components/typography";
import { Card, CardContent } from "@/components/ui/card";

interface LayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
  }>;
}

export default async function EmployeeWizardLayout({ children, params }: LayoutProps) {
  const { lang } = await params;

  // Get dictionary
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageTitle
        description={
          dictionary.employee?.wizard?.description ||
          "Create a new employee using this step-by-step wizard"
        }
      >
        {dictionary.employee?.wizard?.title || "Employee Creation Wizard"}
      </PageTitle>
      <Card>
        <CardContent className="pt-6">{children}</CardContent>
      </Card>
    </div>
  );
}
