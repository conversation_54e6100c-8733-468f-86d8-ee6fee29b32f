"use client";

import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Request } from "../lib/types";
import { RequestTableRow } from "./RequestTableRow";
import { Button } from "@/components/ui/button";
import { ArrowDown, ArrowUp, ArrowUpDown } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

interface RequestTableProps {
  requests: Request[];
  lang: string;
  dictionary: any;
}

type SortField =
  | "id"
  | "contacts"
  | "service_id"
  | "location_id"
  | "status"
  | "created_at"
  | "updated_at";
type SortDirection = "asc" | "desc";

/**
 * RequestTable component
 * Displays requests in a tabular format with sortable columns
 */
export function RequestTable({ requests, lang, dictionary }: RequestTableProps) {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get sort parameters from URL
  const sortField = (searchParams.get("sort") || "created_at") as SortField;
  const sortDirection = (searchParams.get("dir") || "desc") as SortDirection;

  // Handle sorting
  const handleSort = (field: SortField) => {
    // Create a new URLSearchParams object
    const params = new URLSearchParams(searchParams);

    // If the field is already being sorted, toggle the direction
    if (field === sortField) {
      params.set("dir", sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Otherwise, set the new field and default to ascending
      params.set("sort", field);
      params.set("dir", "asc");
    }

    // Update the URL with the new search parameters
    router.push(`?${params.toString()}`);
  };

  // Get sort icon based on current sort state
  const getSortIcon = (field: SortField) => {
    if (field !== sortField) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />;
    }

    return sortDirection === "asc" ? (
      <ArrowUp className="ml-2 h-4 w-4" />
    ) : (
      <ArrowDown className="ml-2 h-4 w-4" />
    );
  };

  // If there are no requests, show a message
  if (requests.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">
          {dictionary.noRequests || "No requests found. Create your first request to get started."}
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort("id")}
                className="p-0 h-auto font-medium"
              >
                {dictionary.request?.fields?.referenceNumber || "Reference Number"}
                {getSortIcon("id")}
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort("contacts")}
                className="p-0 h-auto font-medium"
              >
                {dictionary.request?.fields?.contacts || "Contacts"}
                {getSortIcon("contacts")}
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort("service_id")}
                className="p-0 h-auto font-medium"
              >
                {dictionary.request?.fields?.service || "Service"}
                {getSortIcon("service_id")}
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort("location_id")}
                className="p-0 h-auto font-medium"
              >
                {dictionary.request?.fields?.location || "Location"}
                {getSortIcon("location_id")}
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort("created_at")}
                className="p-0 h-auto font-medium"
              >
                {dictionary.request?.fields?.createdAt || "Created At"}
                {getSortIcon("created_at")}
              </Button>
            </TableHead>
            <TableHead>
              <Button
                variant="ghost"
                onClick={() => handleSort("status")}
                className="p-0 h-auto font-medium"
              >
                {dictionary.request?.fields?.status || "Status"}
                {getSortIcon("status")}
              </Button>
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {requests.map((request) => (
            <RequestTableRow
              key={request.id}
              request={request}
              lang={lang}
              dictionary={dictionary}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
