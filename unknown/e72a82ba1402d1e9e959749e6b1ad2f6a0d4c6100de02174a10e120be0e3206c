-- Create Case File Domain Tables
-- This migration creates the core case file management system with state management,
-- contact relationships, and audit trail functionality.

-- Case file status enum
CREATE TYPE case_file_status AS ENUM ('opening', 'active', 'suspended', 'closed');

-- Main case files table
CREATE TABLE case_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  request_id UUID NOT NULL REFERENCES requests(id) ON DELETE CASCADE,
  case_number TEXT NOT NULL, -- CF-001, CF-002 (organization scoped)
  status case_file_status NOT NULL DEFAULT 'opening',

  -- Assignment and ownership
  created_by UUID NOT NULL REFERENCES auth.users(id),
  assigned_to UUID REFERENCES employees(id),

  -- State transition timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  opened_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  activated_at TIMESTAMP WITH TIME ZONE,
  suspended_at TIMESTAMP WITH TIME ZONE,
  closed_at TIMESTAMP WITH TIME ZONE,

  -- Flexible metadata
  metadata JSONB DEFAULT '{}',

  -- Constraints
  UNIQUE(organization_id, case_number),
  CONSTRAINT fk_case_files_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_case_files_request FOREIGN KEY (request_id) REFERENCES requests(id),
  CONSTRAINT fk_case_files_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT fk_case_files_assigned_to FOREIGN KEY (assigned_to) REFERENCES employees(id)
);

-- Case file contacts junction table (links to existing contacts)
CREATE TABLE case_file_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  case_file_id UUID NOT NULL REFERENCES case_files(id) ON DELETE CASCADE,
  contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
  relationship_type TEXT NOT NULL, -- From request_contacts
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Constraints
  CONSTRAINT fk_case_file_contacts_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_case_file_contacts_case_file FOREIGN KEY (case_file_id) REFERENCES case_files(id),
  CONSTRAINT fk_case_file_contacts_contact FOREIGN KEY (contact_id) REFERENCES contacts(id),
  UNIQUE(case_file_id, contact_id, relationship_type)
);

-- Case file history table (audit trail)
CREATE TABLE case_file_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  case_file_id UUID NOT NULL REFERENCES case_files(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  action TEXT NOT NULL, -- 'status_change', 'assignment', 'created', etc.
  changes JSONB NOT NULL, -- Structured change data
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Constraints
  CONSTRAINT fk_case_file_history_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_case_file_history_case_file FOREIGN KEY (case_file_id) REFERENCES case_files(id),
  CONSTRAINT fk_case_file_history_user FOREIGN KEY (user_id) REFERENCES auth.users(id)
);

-- Create indexes for performance
CREATE INDEX idx_case_files_organization_id ON case_files(organization_id);
CREATE INDEX idx_case_files_request_id ON case_files(request_id);
CREATE INDEX idx_case_files_status ON case_files(status);
CREATE INDEX idx_case_files_assigned_to ON case_files(assigned_to);
CREATE INDEX idx_case_files_created_by ON case_files(created_by);
CREATE INDEX idx_case_files_case_number ON case_files(case_number);

CREATE INDEX idx_case_file_contacts_organization_id ON case_file_contacts(organization_id);
CREATE INDEX idx_case_file_contacts_case_file_id ON case_file_contacts(case_file_id);
CREATE INDEX idx_case_file_contacts_contact_id ON case_file_contacts(contact_id);

CREATE INDEX idx_case_file_history_organization_id ON case_file_history(organization_id);
CREATE INDEX idx_case_file_history_case_file_id ON case_file_history(case_file_id);
CREATE INDEX idx_case_file_history_user_id ON case_file_history(user_id);
CREATE INDEX idx_case_file_history_action ON case_file_history(action);

-- Create triggers for updated_at
CREATE TRIGGER update_case_files_updated_at
BEFORE UPDATE ON case_files
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE case_files IS 'Main case file management table with state tracking and organization scoping';
COMMENT ON TABLE case_file_contacts IS 'Junction table linking case files to contacts with relationship types';
COMMENT ON TABLE case_file_history IS 'Audit trail for all case file changes and state transitions';

COMMENT ON COLUMN case_files.case_number IS 'Organization-scoped case number (CF-001, CF-002, etc.)';
COMMENT ON COLUMN case_files.status IS 'Case file state: opening, active, suspended, closed';
COMMENT ON COLUMN case_files.metadata IS 'Flexible JSONB field for additional case file data';
COMMENT ON COLUMN case_file_history.changes IS 'JSONB field containing structured change data';