"use server";

import { revalidatePath } from "next/cache";
import { dtoDraftData } from "../../../lib/dto/dtoDraftData";
import { updateDraft } from "../../../actions/drafts/update";
import { getCurrentLanguage } from "../../../actions/wizard/navigationUtils";
import { logger } from "@/lib/logger/services/LoggerService";
import { J<PERSON> } from "@/lib/types/database.types";
import { ContactService } from "@/app/[lang]/protected/contact/(features)/management/lib/services/ContactService";

/**
 * Add a contact to the request draft
 * @param draftId The draft ID
 * @param formData The form data
 */
export async function addContact(draftId: string, formData: FormData) {
  try {
    // Process draft data
    const { draft, updatedData } = await dtoDraftData(draftId, formData);

    // Get form values
    const contactId = formData.get("contact_id") as string;
    const relationshipType = formData.get("relationship_type") as string;

    // Validate input
    if (!contactId) {
      throw new Error("Contact ID is required");
    }

    if (!relationshipType) {
      throw new Error("Relationship type is required");
    }

    // Fetch the contact name from the database
    let contactName = "Contact " + contactId.substring(0, 8);
    try {
      const contactResult = await ContactService.view(contactId);
      if (contactResult.success && contactResult.data) {
        contactName = contactResult.data.name;
      }
    } catch (error) {
      logger.error(`Error fetching contact name: ${error}`);
      // Continue with the placeholder name
    }

    // Ensure updatedData is an object
    const dataObj =
      typeof updatedData === "object" && updatedData !== null
        ? (updatedData as Record<string, any>)
        : {};

    // Get current related contacts or initialize empty array
    const relatedContacts = Array.isArray(dataObj.relatedContacts)
      ? [...dataObj.relatedContacts]
      : [];

    // Add new contact
    relatedContacts.push({
      id: contactId,
      name: contactName,
      relationshipType: relationshipType,
    });

    // Update draft data with new related contacts
    const newData = {
      ...dataObj,
      relatedContacts,
    } as Json;

    // Update draft
    await updateDraft(draftId, newData, draft.current_step);

    // Get current language
    const lang = await getCurrentLanguage();

    // Revalidate the path to refresh the page data
    revalidatePath(`/${lang}/protected/automation/wizard/${draftId}/${draft.current_step}`);
  } catch (error) {
    logger.error(`Error adding contact: ${error}`);
    throw new Error(`Failed to add contact: ${error}`);
  }
}

/**
 * Remove a contact from the request draft
 * @param draftId The draft ID
 * @param formData The form data
 */
export async function removeContact(draftId: string, formData: FormData) {
  try {
    // Process draft data
    const { draft, updatedData } = await dtoDraftData(draftId, formData);

    // Get the index of the contact to remove
    const contactIndexStr = formData.get("remove_contact") as string;
    logger.verbose(`Remove contact index string: ${contactIndexStr}`);
    const contactIndex = parseInt(contactIndexStr);

    // Ensure updatedData is an object
    const dataObj =
      typeof updatedData === "object" && updatedData !== null
        ? (updatedData as Record<string, any>)
        : {};

    // Get current related contacts or initialize empty array
    const relatedContacts = Array.isArray(dataObj.relatedContacts)
      ? [...dataObj.relatedContacts]
      : [];

    // Validate index
    if (isNaN(contactIndex) || contactIndex < 0 || contactIndex >= relatedContacts.length) {
      throw new Error("Invalid contact index");
    }

    // Remove contact at the specified index
    relatedContacts.splice(contactIndex, 1);

    // Update draft data with updated related contacts
    const newData = {
      ...dataObj,
      relatedContacts,
    } as Json;

    // Update draft
    await updateDraft(draftId, newData, draft.current_step);

    // Get current language
    const lang = await getCurrentLanguage();

    // Revalidate the path to refresh the page data
    revalidatePath(`/${lang}/protected/automation/wizard/${draftId}/${draft.current_step}`);
  } catch (error) {
    logger.error(`Error removing contact: ${error}`);
    throw new Error(`Failed to remove contact: ${error}`);
  }
}
