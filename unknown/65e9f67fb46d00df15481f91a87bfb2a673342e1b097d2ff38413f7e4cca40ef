version: '3'

services:
  n8n:
    image: n8nio/n8n:latest
    restart: always
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=0.0.0.0         # Better than localhost for container access
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=development
      - WEBHOOK_URL=http://localhost:5678/
      - GENERIC_TIMEZONE=America/Toronto

      # Optional: If these are used by n8n (custom integration?), otherwise omit
      - NEXTJS_HOST=host.docker.internal
      - NEXTJS_PORT=3000
      - NEXTJS_URL=http://host.docker.internal:3000

    volumes:
      - n8n_data:/home/<USER>/.n8n

    networks:
      - supabase_network
      - default

    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  n8n_data:

networks:
  supabase_network:
    external: true
    name: supabase_network_rqrsda2025
  default:
    driver: bridge
