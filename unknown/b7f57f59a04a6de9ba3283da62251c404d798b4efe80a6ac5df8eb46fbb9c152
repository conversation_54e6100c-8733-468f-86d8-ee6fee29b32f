import { getDictionary } from "@/lib/i18n/cache";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { UserProfileService } from "../../../../lib/services/UserProfileService";
import { SettingsForm } from "./components/SettingsForm";

interface SettingsPageProps {
  params: Promise<{
    lang: string;
  }>;
}

export default async function SettingsPage({ params }: SettingsPageProps) {
  // Await the params (still needed for the form)
  const { lang } = await params;

  // Get user profile
  const profile = await UserProfileService.getCurrentUserProfile();
  if (!profile) {
    // Handle case where profile couldn't be loaded
    throw new Error("Failed to load user profile");
  }

  // Get dictionary using the cached function that automatically determines the best language
  const dictionary = await getDictionary();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{dictionary.user.settings}</CardTitle>
        <CardDescription>{dictionary.user.settingsDescription}</CardDescription>
      </CardHeader>
      <CardContent>
        <SettingsForm lang={lang} dictionary={dictionary} profile={profile} />
      </CardContent>
    </Card>
  );
}
