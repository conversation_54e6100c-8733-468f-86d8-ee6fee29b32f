"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useParams, useRouter } from "next/navigation";
import { FileQuestion, Home, ArrowLeft } from "lucide-react";
import { logger } from "@/lib/logger/services/LoggerService";
import DOMAIN_CONFIG from "../../lib/config/domain";

/**
 * Not Found component for the Request view page
 * Displays when a request with the specified ID doesn't exist
 */
export default function RequestNotFound() {
  const params = useParams();
  const router = useRouter();
  const lang = params.lang as string;
  const id = params.id as string;

  // Log the attempted access for debugging
  useEffect(() => {
    logger.warn(`Attempted to access non-existent request with ID: ${id}`);
  }, [id]);

  // Handle navigation back to the list page
  const handleBackToList = () => {
    router.push(`/${lang}${DOMAIN_CONFIG.basePath}/list`);
  };

  // Handle navigation to the dashboard
  const handleBackToDashboard = () => {
    router.push(`/${lang}/protected/dashboard`);
  };

  return (
    <div className="flex items-center justify-center min-h-[70vh]">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <FileQuestion className="h-16 w-16 text-muted-foreground" />
          </div>
          <CardTitle className="text-2xl">Request Not Found</CardTitle>
          <CardDescription>
            The request with ID {id} could not be found. It may have been deleted or you may not
            have permission to view it.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center text-muted-foreground">
          <p>
            If you believe this is an error, please contact your administrator or try again later.
          </p>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-2 justify-center">
          <Button variant="outline" onClick={handleBackToList} className="w-full sm:w-auto">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Requests
          </Button>
          <Button onClick={handleBackToDashboard} className="w-full sm:w-auto">
            <Home className="mr-2 h-4 w-4" />
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
