import { <PERSON>, <PERSON><PERSON> } from "@/lib/types/database.types";

/**
 * Request Status type
 * Defines all possible statuses for a request
 */
export type RequestStatus =
  | "draft" // Initial state during wizard creation
  | "processing" // Request has been submitted and is being processed
  | "waitlist" // Service cannot be provided immediately
  | "completed" // Service can be provided, case file initiated
  | "closed"; // Request has been manually closed

/**
 * Request interface
 * Represents a single request
 * Uses the database Row type for consistency
 */
export type Request = Database["public"]["Tables"]["requests"]["Row"] & {
  status: RequestStatus; // Override status to use our enum type
  service?: {
    id: string;
    name: string;
    description?: string;
    price?: number;
    duration?: number;
    status?: string;
  };
  location?: {
    id: string;
    name: string;
    address?: string;
    emails?: any;
    phones?: any;
    status?: string;
  };
  request_contacts?: any[];
  contacts?: any[];
};

/**
 * Request Insert interface
 * Used when creating a new request
 * Uses the database Insert type for consistency
 */
export type RequestInsert = Omit<Database["public"]["Tables"]["requests"]["Insert"], "status"> & {
  status?: RequestStatus; // Override status to use our enum type
};

/**
 * Request Update interface
 * Used when updating an existing request
 * Uses the database Update type for consistency
 */
export type RequestUpdate = Omit<Database["public"]["Tables"]["requests"]["Update"], "status"> & {
  status?: RequestStatus; // Override status to use our enum type
};

/**
 * Request History interface
 * Represents a single history entry for a request
 * Uses the database Row type for consistency
 */
export type RequestHistory = Omit<
  Database["public"]["Tables"]["request_history"]["Row"],
  "previous_status" | "new_status" | "changes"
> & {
  previous_status: RequestStatus | null;
  new_status: RequestStatus | null;
  changes: Record<string, any> | null;
};

/**
 * Request Metadata interface
 * Represents metadata associated with a request
 * Uses the database Row type for consistency
 */
export type RequestMetadata = Omit<
  Database["public"]["Tables"]["request_metadata"]["Row"],
  "service_requirements" | "family_availability"
> & {
  service_requirements: ServiceRequirements;
  family_availability: FamilyAvailability;
  location?: string; // Added for backward compatibility
};

/**
 * Service Requirements interface
 * Defines the structure for service requirements
 */
export interface ServiceRequirements {
  frequency?: "weekly" | "biweekly" | "monthly" | "custom";
  duration?: number;
  specialRequirements?: string[];
  preferredDays?: (
    | "monday"
    | "tuesday"
    | "wednesday"
    | "thursday"
    | "friday"
    | "saturday"
    | "sunday"
  )[];
  preferredTimeOfDay?: "morning" | "afternoon" | "evening";
  notes?: string;
}

/**
 * Family Availability interface
 * Defines the structure for family availability
 */
export interface FamilyAvailability {
  weekdayAvailability?: {
    monday?: DayAvailability;
    tuesday?: DayAvailability;
    wednesday?: DayAvailability;
    thursday?: DayAvailability;
    friday?: DayAvailability;
    saturday?: DayAvailability;
    sunday?: DayAvailability;
  };
  specificDates?: SpecificDateAvailability[];
  notes?: string;
}

/**
 * Day Availability interface
 * Defines the structure for a day's availability
 */
export interface DayAvailability {
  available: boolean;
  timeSlots?: TimeSlot[];
}

/**
 * Specific Date Availability interface
 * Defines the structure for a specific date's availability
 */
export interface SpecificDateAvailability {
  date: string;
  available: boolean;
  timeSlots?: TimeSlot[];
}

/**
 * Time Slot interface
 * Defines the structure for a time slot
 */
export interface TimeSlot {
  start: string;
  end: string;
}

/**
 * Request With Relations interface
 * Represents a request with related data
 */
export interface RequestWithRelations extends Request {
  metadata?: RequestMetadata;
  history?: RequestHistory[];
  contacts?: RequestContact[];
  request_contacts?: RequestContact[];
  service?: {
    id: string;
    name: string;
    description?: string;
    price?: number;
    duration?: number;
    status?: string;
  };
  location?: {
    id: string;
    name: string;
    address?: string;
    emails?: any;
    phones?: any;
    status?: string;
  };
  requester?: {
    id: string;
    email: string;
    name?: string;
  };
  assignee?: {
    id: string;
    email: string;
    name?: string;
  };
}

/**
 * Request Contact interface
 * Represents a contact associated with a request
 * Uses the database Row type for consistency
 */
export type RequestContact = Database["public"]["Tables"]["request_contacts"]["Row"] & {
  contact?: {
    id: string;
    name: string;
    email?: string | Json;
    phone?: string | Json;
    address?: string;
  };
};

/**
 * Request List Parameters interface
 * Used for filtering and pagination in list operations
 */
export interface RequestListParams {
  page?: number;
  limit?: number;
  status?: RequestStatus | RequestStatus[];
  search?: string;
  sortBy?: keyof Request;
  sortOrder?: "asc" | "desc";
  assignee_id?: string;
  requester_id?: string;
  from_date?: string;
  to_date?: string;
}
