{"name": "Status Running Node (Fixed)", "nodes": [{"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.executionId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "running"}, {"fieldId": "n8n_execution_id", "fieldValue": "={{ $execution.id }}"}, {"fieldId": "started_at", "fieldValue": "={{ $now.toISOString() }}"}, {"fieldId": "updated_at", "fieldValue": "={{ $now.toISOString() }}"}]}}, "id": "node_2", "name": "Update Status to Running", "type": "n8n-nodes-base.supabase", "position": [200, 300], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-260, 300], "id": "0c464979-685e-40df-9a1b-ed08e4178aee", "name": "When Executed by Another Workflow"}], "pinData": {}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Update Status to Running", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8334f2a4-87c0-43a4-9a77-c3fdd95c0699", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "ExJiiEtCksRpM0du", "tags": [{"createdAt": "2025-05-19T01:18:30.835Z", "updatedAt": "2025-05-19T01:18:30.835Z", "id": "Dnsl9yLb11FuJixk", "name": "Execution"}]}