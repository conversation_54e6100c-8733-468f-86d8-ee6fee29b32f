import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";

// Import wizard registrations
import "./lib/config/registerWizards";

interface PageProps {
  params: Promise<{
    lang: string;
  }>;
}

/**
 * Automation dashboard page
 */
export default async function AutomationPage({ params }: PageProps) {
  const { lang } = await params;

  // Get dictionary for translations
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">{dictionary.automation.dashboard.title}</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{dictionary.automation.dashboard.employeeCreation.title}</CardTitle>
            <CardDescription>
              {dictionary.automation.dashboard.employeeCreation.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {dictionary.automation.dashboard.employeeCreation.content}
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href={`/${lang}/protected/automation/employee-wizard`}>
                {dictionary.automation.dashboard.employeeCreation.startButton}
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{dictionary.automation.dashboard.requestCreation.title}</CardTitle>
            <CardDescription>
              {dictionary.automation.dashboard.requestCreation.description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {dictionary.automation.dashboard.requestCreation.content}
            </p>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href={`/${lang}/protected/automation/request-wizard`}>
                {dictionary.automation.dashboard.requestCreation.startButton}
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Additional automation cards can be added here */}
      </div>
    </div>
  );
}
