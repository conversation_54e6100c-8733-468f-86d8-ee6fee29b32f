import { ReactNode } from "react";
import { i18n } from "@/lib/i18n/services/I18nService";
import { PageTitle } from "@/components/typography";

interface LayoutProps {
  children: ReactNode;
  params: Promise<{
    lang: string;
    executionId: string;
  }>;
}

export default async function ExecutionLayout({ children, params }: LayoutProps) {
  const { lang } = await params;

  // Get dictionary
  const dictionary = i18n.getDictionary(lang);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <PageTitle
        description={
          dictionary.automation?.execution?.description ||
          "Track the status of your workflow execution"
        }
      >
        {dictionary.automation?.execution?.title || "Workflow Execution Status"}
      </PageTitle>
      {children}
    </div>
  );
}
