import { <PERSON>, Json } from "@/lib/types/database.types";

/**
 * Generic draft types with type parameter for data
 */
export type Draft<TData = Json> = Database["public"]["Tables"]["drafts"]["Row"] & { data: TData };

export type DraftInsert<TData = Json> = Omit<
  Database["public"]["Tables"]["drafts"]["Insert"],
  "data"
> & {
  data: TData;
};

export type DraftUpdate<TData = Json> = Partial<
  Omit<Database["public"]["Tables"]["drafts"]["Update"], "data">
> & {
  data?: TData;
};

/**
 * Generic workflow type
 */
export type WorkflowType = string;

/**
 * Draft filters for listing drafts
 */
export interface DraftFilters {
  userId?: string;
  organizationId?: string;
  workflowType?: WorkflowType;
}
