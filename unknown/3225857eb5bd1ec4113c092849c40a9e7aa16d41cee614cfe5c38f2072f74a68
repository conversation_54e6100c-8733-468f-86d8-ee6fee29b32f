"use client";

import { But<PERSON> } from "@/components/ui/button";
import { nextStep, previousStep, submitStep } from "../../../actions/wizard";
import { WizardStep } from "../../../lib/types/wizard";

interface WizardNavigationProps {
  draftId: string;
  currentStep: number;
  step: WizardStep;
  isLastStep: boolean;
  isSubmitting?: boolean;
  buttonLabels: {
    previous: string;
    next: string;
    submit: string;
    submitting: string;
  };
}

/**
 * Wizard navigation component
 * Displays the navigation buttons for the wizard
 */
export default function WizardNavigation({
  draftId,
  step,
  isSubmitting = false,
  buttonLabels,
}: WizardNavigationProps) {
  return (
    <div className="flex justify-between mt-8">
      <Button
        variant="outline"
        formAction={previousStep.bind(null, draftId)}
        form="wizard-form"
        type="submit"
        value="previous"
        disabled={!Boolean(step?.previousStepId ?? false)}
      >
        {buttonLabels.previous}
      </Button>

      <div className="flex gap-2">
        {step?.isFinalStep && (
          <Button
            formAction={submitStep.bind(null, draftId)}
            form="wizard-form"
            type="submit"
            value="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? buttonLabels.submitting : buttonLabels.submit}
          </Button>
        )}

        {step?.nextStepId && (
          <Button formAction={nextStep.bind(null, draftId)} disabled={isSubmitting}>
            {buttonLabels.next}
          </Button>
        )}
      </div>
    </div>
  );
}
