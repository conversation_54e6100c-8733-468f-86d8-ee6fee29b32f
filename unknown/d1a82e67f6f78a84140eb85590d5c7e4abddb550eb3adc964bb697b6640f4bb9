"use server";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";
import { EmployeeDraft, EmployeeInsert } from "../../types/employee";

interface BasicInfoStepProps {
  draft: EmployeeDraft;
}

/**
 * Basic information step for employee creation
 */
export default async function BasicInfoStep({ draft }: BasicInfoStepProps) {
  const data = draft.data || ({} as EmployeeInsert);
  const dictionary = await getDomainFeatureDictionary("employee", "wizard");

  return (
    <div className="space-y-6">
      <div className="grid gap-3">
        <Label htmlFor="first_name" className="font-medium">
          {dictionary.fields.firstName} <span className="text-destructive">*</span>
        </Label>
        <Input
          id="first_name"
          name="first_name"
          defaultValue={data.first_name || ""}
          placeholder={dictionary.placeholders?.enterFirstName || "Enter first name"}
          required
        />
      </div>

      <div className="grid gap-3">
        <Label htmlFor="last_name" className="font-medium">
          {dictionary.fields.lastName} <span className="text-destructive">*</span>
        </Label>
        <Input
          id="last_name"
          name="last_name"
          defaultValue={data.last_name || ""}
          placeholder={dictionary.placeholders?.enterLastName || "Enter last name"}
          required
        />
      </div>

      <div className="grid gap-3">
        <Label htmlFor="job_title" className="font-medium">
          {dictionary.fields.jobTitle || "Job Title"}
        </Label>
        <Input
          id="job_title"
          name="job_title"
          defaultValue={data.job_title || ""}
          placeholder={dictionary.placeholders?.enterJobTitle || "Enter job title"}
        />
      </div>
    </div>
  );
}
