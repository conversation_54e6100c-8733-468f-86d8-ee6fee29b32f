"use server";

import { logger } from "@/lib/logger/services/LoggerService";
import { auth } from "@/lib/authentication/services/AuthenticationService";

/**
 * Handle navigation errors by logging them and redirecting
 * @param action The action being performed (e.g., "nextStep", "previousStep", "submitStep")
 * @param error The error that occurred
 * @param fallbackUrl Optional fallback URL to redirect to
 */
export async function logNavigationError(
  action: string,
  error: unknown,
  _fallbackUrl?: string
): Promise<void> {
  logger.error(`Error in ${action}: ${error}`);
}

/**
 * Handle navigation errors by logging them and redirecting
 * @param action The action being performed (e.g., "nextStep", "previousStep", "submitStep")
 * @param error The error that occurred
 * @param fallbackUrl Optional fallback URL to redirect to
 */
export async function handleNavigationError(
  action: string,
  error: unknown,
  _fallbackUrl?: string
): Promise<void> {
  await logNavigationError(action, error);
}

/**
 * Get the current user's language
 * @returns The current user's language or "en" as default
 */
export async function getCurrentLanguage(): Promise<string> {
  return (await auth.getCurrentUserProfile())?.language || "en";
}

/**
 * Build a path with the correct language prefix
 * @param path The path without language prefix
 * @param lang The language to use
 * @returns The full path with language prefix
 */
export async function buildLanguagePath(path: string, lang: string) {
  return path.startsWith("/") ? `/${lang}${path}` : `/${lang}/${path}`;
}

/**
 * Redirect to a specific step in the wizard
 * @param draftId The draft ID
 * @param stepId The step ID
 */
export async function redirectToStep(draftId: string, stepId: string) {
  // This is a placeholder function that will be implemented with redirect logic
  // For now, it's just a stub to satisfy the import in completeStep.ts
  // Suppress unused parameter warnings
  void draftId;
  void stepId;
}
