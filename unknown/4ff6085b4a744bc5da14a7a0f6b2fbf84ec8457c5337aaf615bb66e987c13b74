{"title": "<PERSON><PERSON><PERSON>", "description": "G<PERSON>rer les demandes de service", "createTitle": "<PERSON><PERSON><PERSON> une nouvelle demande", "createDescription": "Entrez les détails de la nouvelle demande", "editTitle": "Modifier la demande", "editDescription": "Mettre à jour les détails de cette demande", "viewTitle": "<PERSON><PERSON><PERSON> de la demande", "removeTitle": "Supprimer la demande", "removeDescription": "Êtes-vous sûr de vouloir supprimer cette demande?", "removeConfirm": "<PERSON><PERSON>, supprimer", "removeCancel": "Non, annuler", "backToList": "Retour à la liste", "requestCreated": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "requestUpdated": "<PERSON><PERSON><PERSON> mise à jour avec succès", "requestRemoved": "De<PERSON>e supprimée avec succès", "noRequests": "Aucune demande trouvée. Créez votre première demande pour commencer.", "fields": {"title": "Titre", "description": "Description", "serviceType": "Type de service", "priority": "Priorité", "status": "Statut", "requester": "De<PERSON>eur", "assignee": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le"}, "priorities": {"low": "<PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "high": "Haute"}, "statuses": {"draft": "Brouillon", "requested": "Soumise", "waitlist": "Liste d'attente", "approved": "A<PERSON><PERSON><PERSON><PERSON>", "rejected": "Rejetée", "completed": "Complétée"}, "serviceTypes": {"supervised_visit": "Visite supervisée", "exchange": "Échange", "consultation": "Consultation", "other": "<PERSON><PERSON>"}, "actions": {"submit": "Soumettre la demande", "approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "waitlist": "Ajouter à la liste d'attente", "complete": "Marquer comme complétée", "viewHistory": "Voir l'historique", "compareChanges": "Comparer les changements"}, "validation": {"titleRequired": "Le titre est requis", "titleTooLong": "Le titre est trop long (max 255 caractères)", "descriptionRequired": "La description est requise", "serviceTypeRequired": "Le type de service est requis"}}