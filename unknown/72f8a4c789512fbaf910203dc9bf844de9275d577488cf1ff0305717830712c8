{"name": "Notification Success", "nodes": [{"parameters": {}, "id": "f42c10cd-f27e-46c1-bcce-836c21916025", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-180, 300], "typeVersion": 1}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [740, 420], "id": "91f7dfdd-db3d-4e89-8c99-9a98c2a38e80", "name": "No Operation, do nothing"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "91462c76-8f8a-4482-9500-b1666d3d1deb", "leftValue": "={{ $json.user_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "12083925-14e5-4d00-9ea0-ad4c35d1f2fe", "leftValue": "={{ $json.organization_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "00afa2dd-893b-48b5-bb86-bbf57e219704", "leftValue": "={{ $json.message }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}, {"id": "345283fe-bcee-469d-8913-59facc1087a2", "leftValue": "={{ $json.title }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [340, 300], "id": "02dba8f9-39f7-496f-a2cd-0d48f5fc99b0", "name": "Validation"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"type\": \"success\",\n  \"title\": \"{{ $json.title }}\",\n  \"message\": \"{{ $json.message }}\",\n  \"user_id\": \"{{ $json.user_id}}\",\n  \"organization_id\": \"{{ $json.organization_id }}\",\n  \"read\": false\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 300], "id": "7e0b3118-08e0-436f-b43e-388c7e8225ef", "name": "DTO"}, {"parameters": {"tableId": "notifications", "dataToSend": "autoMapInputData"}, "id": "c1c516f7-1f57-4250-a2f9-e34e8ac948e7", "name": "Create Success Notification", "type": "n8n-nodes-base.supabase", "position": [740, 160], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "pinData": {"When Called From Another Workflow": [{"json": {"title": "Notification Success", "message": "This is a notification success.", "user_id": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "organization_id": "00000000-0000-0000-0000-000000000001"}}]}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "DTO", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Create Success Notification", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "DTO": {"main": [[{"node": "Validation", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "65b90ce6-528d-4ee7-b512-ee88aa53e1bd", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "dxlrwZJcwvFimlOG", "tags": [{"createdAt": "2025-05-19T01:17:10.835Z", "updatedAt": "2025-05-19T01:17:10.835Z", "id": "6OnnSnIaUdsuTYbO", "name": "Notification"}]}