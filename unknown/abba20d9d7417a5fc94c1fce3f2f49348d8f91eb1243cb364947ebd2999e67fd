{"title": "Requests", "description": "Manage service requests", "createTitle": "Create New Request", "createDescription": "Enter the details for the new request", "editTitle": "Edit Request", "editDescription": "Update the details for this request", "viewTitle": "Request Details", "removeTitle": "Remove Request", "removeDescription": "Are you sure you want to remove this request?", "removeConfirm": "Yes, Remove", "removeCancel": "No, Cancel", "backToList": "Back to List", "requestCreated": "Request created successfully", "requestUpdated": "Request updated successfully", "requestRemoved": "Request removed successfully", "noRequests": "No requests found. Create your first request to get started.", "fields": {"title": "Title", "description": "Description", "serviceType": "Service Type", "priority": "Priority", "status": "Status", "requester": "Requester", "assignee": "Assignee", "createdAt": "Created At", "updatedAt": "Updated At"}, "priorities": {"low": "Low", "medium": "Medium", "high": "High"}, "statuses": {"draft": "Draft", "requested": "Requested", "waitlist": "Waitlist", "approved": "Approved", "rejected": "Rejected", "completed": "Completed"}, "serviceTypes": {"supervised_visit": "Supervised Visit", "exchange": "Exchange", "consultation": "Consultation", "other": "Other"}, "actions": {"submit": "Submit Request", "approve": "Approve", "reject": "Reject", "waitlist": "Add to Waitlist", "complete": "Mark as Complete", "viewHistory": "View History", "compareChanges": "Compare Changes"}, "validation": {"titleRequired": "Title is required", "titleTooLong": "Title is too long (max 255 characters)", "descriptionRequired": "Description is required", "serviceTypeRequired": "Service type is required"}}