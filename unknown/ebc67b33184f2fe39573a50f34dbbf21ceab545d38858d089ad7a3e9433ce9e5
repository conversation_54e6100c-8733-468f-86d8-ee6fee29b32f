import { WizardConfig, WizardStep } from "../../../lib/types";

// Import step components
import BasicInfoStep from "../components/steps/BasicInfoStep";
import ContactInfoStep from "../components/steps/ContactInfoStep";
import ReviewStep from "../components/steps/ReviewStep";

/**
 * Employee wizard configuration
 */
export const employeeWizardConfig: WizardConfig = {
  workflowType: "employee_creation",
  title: "Create Employee",
  description: "Complete the following steps to create a new employee",
  initialStep: "basic_info",
  stepsMap: new Map<string, WizardStep>([
    [
      "basic_info",
      {
        id: "basic_info",
        label: "Basic Info",
        nextStepId: "contact_info",
        component: BasicInfoStep,
      },
    ],
    [
      "contact_info",
      {
        id: "contact_info",
        label: "Contact",
        nextStepId: "review",
        previousStepId: "basic_info",
        component: ContactInfoStep,
      },
    ],
    [
      "review",
      {
        id: "review",
        label: "Review",
        previousStepId: "contact_info",
        isFinalStep: true,
        component: ReviewStep,
      },
    ],
  ]),
};
