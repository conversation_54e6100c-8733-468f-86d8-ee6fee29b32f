"use server";
import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { EmployeeDraftInsert } from "../../types/employee";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";

interface ReviewStepProps {
  draft: EmployeeDraftInsert;
}

/**
 * Review step for employee creation
 */
export default async function ReviewStep({ draft }: ReviewStepProps) {
  const data = draft.data;
  const dictionary = await getDomainFeatureDictionary("employee", "wizard");

  return (
    <div className="space-y-6">
      <p className="text-sm text-muted-foreground mb-4">
        {dictionary.reviewDescription || "Please review the information below before submitting."}
      </p>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/3">{dictionary.reviewTable?.field || "Field"}</TableHead>
            <TableHead>{dictionary.reviewTable?.value || "Value"}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className="font-medium">
              {dictionary.fields?.firstName || "First Name"}
            </TableCell>
            <TableCell>{data.first_name || "-"}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">
              {dictionary.fields?.lastName || "Last Name"}
            </TableCell>
            <TableCell>{data.last_name || "-"}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">
              {dictionary.fields?.jobTitle || "Job Title"}
            </TableCell>
            <TableCell>{data.job_title || "-"}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{dictionary.fields.email}</TableCell>
            <TableCell>{data.email}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">{dictionary.fields?.role || "User Role"}</TableCell>
            <TableCell>{data.role}</TableCell>
          </TableRow>
        </TableBody>
      </Table>

      {/* Hidden input to indicate submission */}
      <input type="hidden" name="submitted" value="true" />
    </div>
  );
}
