import { Draft } from "./draft";

/**
 * Generic wizard step interface
 */
export interface WizardStep {
  /**
   * Unique identifier for the step
   */
  id: string;

  /**
   * Display label for the step
   */
  label: string;

  /**
   * Optional description for the step
   */
  description?: string;

  /**
   * ID of the next step
   */
  nextStepId?: string;
  previousStepId?: string;
  isFinalStep?: boolean;

  /**
   * Component to render for this step
   * The component will receive the draft data
   */
  component: React.ComponentType<{
    draft: Draft<any>;
    searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
  }>;
}

/**
 * Generic wizard configuration
 */
export interface WizardConfig {
  /**
   * The workflow type this wizard is for
   */
  workflowType: string;

  /**
   * Title of the wizard
   */
  title: string;

  /**
   * Optional description of the wizard
   */
  description?: string;

  stepsMap: Map<string, WizardStep>;

  /**
   * ID of the initial step
   */
  initialStep: string;
}
