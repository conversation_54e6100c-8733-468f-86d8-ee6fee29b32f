{"name": "Status Running Node", "nodes": [{"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "=eq", "keyValue": "={{ $json.body.id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "=running"}]}}, "id": "node_2", "name": "Update Status to Running", "type": "n8n-nodes-base.supabase", "position": [200, 300], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-260, 300], "id": "0c464979-685e-40df-9a1b-ed08e4178aee", "name": "When Executed by Another Workflow"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "Update Status to Running", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "tags": [{"name": "Execution"}]}