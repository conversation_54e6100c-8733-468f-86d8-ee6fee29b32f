{"name": "Notification Error", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-180, 300], "typeVersion": 1}, {"parameters": {"tableId": "notifications", "dataToSend": "autoMapInputData"}, "id": "node_3", "name": "Create Error Notification", "type": "n8n-nodes-base.supabase", "position": [740, 160], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [740, 420], "id": "1860bb80-29ed-49ec-8ada-f13defe6c5ff", "name": "No Operation, do nothing"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "91462c76-8f8a-4482-9500-b1666d3d1deb", "leftValue": "={{ $json.user_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "12083925-14e5-4d00-9ea0-ad4c35d1f2fe", "leftValue": "={{ $json.organization_id }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notContains"}}, {"id": "00afa2dd-893b-48b5-bb86-bbf57e219704", "leftValue": "={{ $json.message }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}, {"id": "345283fe-bcee-469d-8913-59facc1087a2", "leftValue": "={{ $json.title }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [340, 300], "id": "fe12c131-8c8e-4dbf-8163-619f9f94e250", "name": "Validation"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"type\": \"error\",\n  \"title\": \"{{ $json.title }}\",\n  \"message\": \"{{ $json.message }}\",\n  \"user_id\": \"{{ $json.user_id}}\",\n  \"organization_id\": \"{{ $json.organization_id }}\",\n  \"read\": false\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 300], "id": "d564f7f2-1700-4f63-b0d8-90e59dd23ffa", "name": "DTO"}], "pinData": {"When Called From Another Workflow": [{"json": {"title": "Execution Failed", "message": "Execution stalled - no updates for over 30 minutes", "user_id": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "organization_id": "00000000-0000-0000-0000-000000000001"}}]}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "DTO", "type": "main", "index": 0}]]}, "Validation": {"main": [[{"node": "Create Error Notification", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "DTO": {"main": [[{"node": "Validation", "type": "main", "index": 0}], []]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e802d7a7-81aa-405c-82af-707cbd1285c4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "5BByydkOieU0V7W5", "tags": [{"createdAt": "2025-05-19T01:17:10.835Z", "updatedAt": "2025-05-19T01:17:10.835Z", "id": "6OnnSnIaUdsuTYbO", "name": "Notification"}]}