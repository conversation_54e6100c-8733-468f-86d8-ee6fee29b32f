import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  // Global ignores - these apply to all configurations
  {
    ignores: [
      "src/components/ui/**", // exclude shadcn component code
      "src/app/**/docs/**", // exclude documentation files
      "src/app/**/design-system/**", // exclude design system examples
      "src/app/**/github-workflow/**", // exclude github workflow docs
      "**/__tests__/**", // exclude test files
      "**/*.test.{js,jsx,ts,tsx}", // exclude test files
      "**/*.spec.{js,jsx,ts,tsx}", // exclude spec files
      "**/examples/**", // exclude example files
    ],
  },
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    rules: {
      // React rules
      "react/prop-types": "off", // TypeScript handles prop types
      "react/react-in-jsx-scope": "off", // Not needed in Next.js
      "react/display-name": "off", // Not needed with function components
      "react/no-unescaped-entities": "off", // Allow quotes in JSX

      // TypeScript rules
      "@typescript-eslint/no-unused-vars": ["warn", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
      "@typescript-eslint/explicit-module-boundary-types": "off", // Not needed with TypeScript inference
      "@typescript-eslint/no-explicit-any": "off", // Discourage use of any
      "@typescript-eslint/no-non-null-assertion": "error", // Discourage non-null assertions

      // General rules
      "no-console": ["warn", { "allow": ["warn", "error"] }], // Allow console.warn and console.error
      "prefer-const": "warn", // Prefer const over let when possible
      "no-var": "error", // No var, use let or const
      "eqeqeq": ["error", "always"], // Always use === and !==
      "no-multiple-empty-lines": ["error", { "max": 1, "maxEOF": 1 }], // Limit empty lines
      "no-trailing-spaces": "error", // No trailing spaces
    },
  },
  {
    // Specific rules for Next.js pages and components
    files: ["src/app/**/*.{js,jsx,ts,tsx}"],
    rules: {
      "import/no-default-export": "off", // Allow default exports in pages
    },
  },
];

export default eslintConfig;
