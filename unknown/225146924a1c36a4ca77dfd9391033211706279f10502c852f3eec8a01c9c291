"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Search, Filter, ChevronDown, X } from "lucide-react";
import { RequestStatus } from "../lib/types";

interface RequestFiltersProps {
  lang: string;
  dictionary: any;
  basePath: string;
}

const REQUEST_STATUSES: RequestStatus[] = [
  "draft",
  "processing",
  "waitlist",
  "completed",
  "closed",
];

export function RequestFilters({ lang, dictionary, basePath }: RequestFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State for filters
  const [search, setSearch] = useState(searchParams.get("search") || "");
  const [selectedStatuses, setSelectedStatuses] = useState<RequestStatus[]>(() => {
    const statusParam = searchParams.get("status");
    return statusParam ? (statusParam.split(",") as RequestStatus[]) : [];
  });
  const [filtersOpen, setFiltersOpen] = useState(false);

  // Update URL when filters change
  const updateFilters = useCallback(
    (newSearch: string, newStatuses: RequestStatus[]) => {
      const params = new URLSearchParams(searchParams);

      // Update search
      if (newSearch.trim()) {
        params.set("search", newSearch.trim());
      } else {
        params.delete("search");
      }

      // Update status filter
      if (newStatuses.length > 0) {
        params.set("status", newStatuses.join(","));
      } else {
        params.delete("status");
      }

      // Reset to first page when filters change
      params.delete("page");

      // Navigate to new URL
      const newUrl = `/${lang}${basePath}/list?${params.toString()}`;
      router.push(newUrl);
    },
    [searchParams, lang, basePath, router]
  );

  // Handle search input change with debounce (only for search, not status)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateFilters(search, selectedStatuses);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [search, updateFilters]);

  // Handle status filter change
  const handleStatusChange = (status: RequestStatus, checked: boolean) => {
    const newStatuses = checked
      ? [...selectedStatuses, status]
      : selectedStatuses.filter((s) => s !== status);

    setSelectedStatuses(newStatuses);
    updateFilters(search, newStatuses);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearch("");
    setSelectedStatuses([]);
    const params = new URLSearchParams(searchParams);
    params.delete("search");
    params.delete("status");
    params.delete("page");

    const newUrl = `/${lang}${basePath}/list${params.toString() ? `?${params.toString()}` : ""}`;
    router.push(newUrl);
  };

  // Get status label
  const getStatusLabel = (status: RequestStatus): string => {
    const statusLabels: Record<RequestStatus, string> = {
      draft: "Draft",
      processing: "Processing",
      waitlist: "Waitlist",
      completed: "Completed",
      closed: "Closed",
    };
    return statusLabels[status] || status;
  };

  const hasActiveFilters = search.trim() || selectedStatuses.length > 0;

  return (
    <div className="space-y-2">
      <div className="flex flex-col sm:flex-row gap-2">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search requests..."
            className="pl-8"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>

        {/* Filter Toggle Button */}
        <Collapsible open={filtersOpen} onOpenChange={setFiltersOpen}>
          <CollapsibleTrigger asChild>
            <Button variant="outline" className="sm:w-auto">
              <Filter className="mr-2 h-4 w-4" />
              Filters
              {hasActiveFilters && (
                <span className="ml-1 bg-primary text-primary-foreground rounded-full px-1.5 py-0.5 text-xs">
                  {(search.trim() ? 1 : 0) + selectedStatuses.length}
                </span>
              )}
              <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </CollapsibleTrigger>

          <CollapsibleContent className="mt-2">
            <div className="border rounded-lg p-4 bg-background space-y-4">
              {/* Status Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Status</Label>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2">
                  {REQUEST_STATUSES.map((status) => (
                    <div key={status} className="flex items-center space-x-2">
                      <Checkbox
                        id={`status-${status}`}
                        checked={selectedStatuses.includes(status)}
                        onCheckedChange={(checked) =>
                          handleStatusChange(status, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`status-${status}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {getStatusLabel(status)}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Clear Filters */}
              {hasActiveFilters && (
                <div className="flex justify-end">
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    <X className="mr-2 h-4 w-4" />
                    Clear filters
                  </Button>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}
