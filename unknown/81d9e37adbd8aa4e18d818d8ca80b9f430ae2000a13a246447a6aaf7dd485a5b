{"name": "Execution Complete", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [-220, 320], "typeVersion": 1}, {"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.executionId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "completed"}, {"fieldId": "result", "fieldValue": "={{ $json.result }}"}, {"fieldId": "updated_at", "fieldValue": "={{ $now.toUTC() }}"}]}}, "id": "node_2", "name": "Update Status to Completed", "type": "n8n-nodes-base.supabase", "position": [140, 320], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "connections": {"When Called From Another Workflow": {"main": [[{"node": "Update Status to Completed", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "tags": [{"name": "Execution"}]}