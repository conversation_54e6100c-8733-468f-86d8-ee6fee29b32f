-- Migration to enhance the request schema for the wizard
-- Adds location_id, service_id, reference_number, start_date, end_date fields to requests table
-- Updates the request_metadata schema documentation

-- Add new columns to the requests table
ALTER TABLE public.requests
ADD COLUMN reference_number TEXT,
ADD COLUMN location_id UUID REFERENCES public.locations(id),
ADD COLUMN service_id UUID REFERENCES public.services(id),
ADD COLUMN start_date DATE,
ADD COLUMN end_date DATE;

-- Create indexes for the new foreign keys
CREATE INDEX IF NOT EXISTS idx_requests_location_id ON public.requests(location_id);
CREATE INDEX IF NOT EXISTS idx_requests_service_id ON public.requests(service_id);

-- Add comments to document the new fields
COMMENT ON COLUMN public.requests.reference_number IS 'Human-readable reference number for the request';
COMMENT ON COLUMN public.requests.location_id IS 'The organization location where the service will be provided';
COMMENT ON COLUMN public.requests.service_id IS 'The specific service being requested';
COMMENT ON COLUMN public.requests.start_date IS 'The date when the service should start';
COMMENT ON COLUMN public.requests.end_date IS 'The date when the service should end';

-- Update the comment on the service_requirements column to document the new schema
COMMENT ON COLUMN public.request_metadata.service_requirements IS 'JSON schema for service requirements:
{
  "frequency": {
    "type": "string",
    "enum": ["daily", "weekly", "biweekly", "monthly", "custom"],
    "description": "How often the service is required"
  },
  "repetition": {
    "type": "integer",
    "description": "Number of times the service should be repeated"
  },
  "duration": {
    "type": "integer",
    "description": "Duration of each service session in minutes"
  },
  "specialRequirements": {
    "type": "array",
    "items": {
      "type": "string"
    },
    "description": "List of special requirements for the service"
  },
  "preferredDays": {
    "type": "array",
    "items": {
      "type": "string",
      "enum": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
    },
    "description": "Preferred days of the week for service"
  },
  "preferredTimeOfDay": {
    "type": "string",
    "enum": ["morning", "afternoon", "evening", "flexible"],
    "description": "Preferred time of day for service"
  },
  "notes": {
    "type": "string",
    "description": "Additional notes about service requirements"
  }
}';

-- Add a comment to document the relationship_type field in request_contacts
COMMENT ON COLUMN public.request_contacts.relationship_type IS 'Type of relationship between the contact and the request. Common values: family, guardian, caregiver, professional, other';

-- Create a function to generate reference numbers for requests
CREATE OR REPLACE FUNCTION public.generate_request_reference()
RETURNS TRIGGER AS $$
DECLARE
  org_prefix TEXT;
  year_suffix TEXT;
  sequence_num INTEGER;
  ref_number TEXT;
BEGIN
  -- Get organization prefix (first 3 letters of organization name)
  SELECT UPPER(SUBSTRING(name FROM 1 FOR 3)) INTO org_prefix
  FROM organizations
  WHERE id = NEW.organization_id;

  -- If no prefix found, use 'REQ'
  IF org_prefix IS NULL THEN
    org_prefix := 'REQ';
  END IF;

  -- Get year suffix (last 2 digits of current year)
  year_suffix := TO_CHAR(CURRENT_DATE, 'YY');

  -- Get next sequence number for this organization and year
  SELECT COALESCE(MAX(SUBSTRING(reference_number FROM '[0-9]+')::INTEGER), 0) + 1 INTO sequence_num
  FROM requests
  WHERE reference_number LIKE org_prefix || '-' || year_suffix || '-%';

  -- Format the reference number: ORG-YY-NNNN
  ref_number := org_prefix || '-' || year_suffix || '-' || LPAD(sequence_num::TEXT, 4, '0');

  -- Set the reference number
  NEW.reference_number := ref_number;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically generate reference numbers
CREATE TRIGGER generate_request_reference_trigger
BEFORE INSERT ON public.requests
FOR EACH ROW
WHEN (NEW.reference_number IS NULL)
EXECUTE FUNCTION public.generate_request_reference();

-- Update the required fields for each status to include the new fields
UPDATE public.request_status_definitions
SET required_fields = required_fields || '["reference_number"]'::jsonb
WHERE status IN ('requested', 'waitlist', 'approved', 'rejected', 'completed');

-- Update the required fields for 'requested' status to include service_id and location_id
UPDATE public.request_status_definitions
SET required_fields = required_fields || '["service_id", "location_id"]'::jsonb
WHERE status = 'requested';