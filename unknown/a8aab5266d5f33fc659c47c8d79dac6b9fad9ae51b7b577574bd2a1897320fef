"use server";

import { Draft } from "../../../../lib/types";
import { Badge } from "@/components/ui/badge";
import { Calendar, MapPin, Briefcase, Users } from "lucide-react";
import { format } from "date-fns";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { getDomainFeatureDictionary } from "@/lib/i18n/cache";

interface ReviewStepProps {
  draft: Draft<any>;
}

/**
 * Review Step for the Request Wizard
 * Displays a summary of all information entered in previous steps
 * and allows the user to submit the request
 */
export default async function ReviewStep({ draft }: ReviewStepProps) {
  // Get data from draft
  const data = draft.data || {};

  // Get dictionary for translations
  const dictionary = await getDomainFeatureDictionary("automation", "request-wizard");

  // Fetch location and service details if IDs are available
  let locationDetails = null;
  let serviceDetails = null;

  if (data.location_id) {
    try {
      const locations = await ProfileService.getLocations();
      locationDetails = locations.find((loc) => loc.id === data.location_id);
    } catch (error) {
      console.error("Error fetching location details:", error);
    }
  }

  if (data.service_id) {
    try {
      const services = await ProfileService.getServices();
      serviceDetails = services.find((svc) => svc.id === data.service_id);
    } catch (error) {
      console.error("Error fetching service details:", error);
    }
  }

  // Format dates if available
  const startDate = data.start_date ? new Date(data.start_date) : null;

  // Get related contacts
  interface RelatedContact {
    id: string;
    name: string;
    relationshipType: string;
  }

  const relatedContacts: RelatedContact[] = data.relatedContacts || [];

  return (
    <div className="bg-black text-white p-6 rounded-lg">
      <div className="space-y-4">
        <div className="mb-2">
          <h3 className="text-lg font-bold">{dictionary.review.title || "Review Request"}</h3>
          <p className="text-xs text-gray-400">{dictionary.review.description}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Left column: Basic Info & Service Requirements */}
          <div className="space-y-4">
            {/* Basic Info Card */}
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-4">
              <div className="flex justify-between items-center border-b border-gray-800 pb-2 mb-3">
                <h4 className="text-sm font-medium">{dictionary.review.basicInfo}</h4>
                {data.service_id && data.location_id && (
                  <Badge
                    variant="outline"
                    className="bg-primary/20 text-xs border-primary/50 text-primary-foreground"
                  >
                    {dictionary.review.complete}
                  </Badge>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                  <div>
                    <p className="text-xs text-gray-400">{dictionary.basic_info.service}</p>
                    <p className="text-sm">
                      {serviceDetails?.name || dictionary.review.notSpecified}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                  <div>
                    <p className="text-xs text-gray-400">{dictionary.basic_info.location}</p>
                    <p className="text-sm">
                      {locationDetails?.name || dictionary.review.notSpecified}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" />
                  <div>
                    <p className="text-xs text-gray-400">
                      {dictionary.service_requirements.startDate}
                    </p>
                    <p className="text-sm">
                      {startDate ? format(startDate, "PPP") : dictionary.review.notSpecified}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Service Requirements Card */}
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-4">
              <div className="flex justify-between items-center border-b border-gray-800 pb-2 mb-3">
                <h4 className="text-sm font-medium">{dictionary.review.serviceRequirements}</h4>
                {data.periodicity && data.frequency_count && data.duration && (
                  <Badge
                    variant="outline"
                    className="bg-primary/20 text-xs border-primary/50 text-primary-foreground"
                  >
                    {dictionary.review.complete}
                  </Badge>
                )}
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <p className="text-xs text-gray-400">
                    {dictionary.service_requirements.periodicity || "Periodicity"}
                  </p>
                  <p className="text-sm">
                    {data.periodicity ? data.periodicity : dictionary.review.notSpecified}
                  </p>
                </div>

                <div>
                  <p className="text-xs text-gray-400">
                    {dictionary.service_requirements.frequencyCount || "Frequency"}
                  </p>
                  <p className="text-sm">
                    {data.frequency_count
                      ? `${data.frequency_count} times per period`
                      : dictionary.review.notSpecified}
                  </p>
                </div>

                <div className="col-span-2">
                  <p className="text-xs text-gray-400">
                    {dictionary.service_requirements.duration}
                  </p>
                  <p className="text-sm">
                    {data.duration ? `${data.duration} minutes` : dictionary.review.notSpecified}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Right column: Related Contacts & Family Availability */}
          <div className="space-y-4">
            {/* Related Contacts Card */}
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-4">
              <div className="flex justify-between items-center border-b border-gray-800 pb-2 mb-3">
                <h4 className="text-sm font-medium">{dictionary.review.relatedContacts}</h4>
                {relatedContacts.length > 0 && (
                  <Badge
                    variant="outline"
                    className="bg-primary/20 text-xs border-primary/50 text-primary-foreground"
                  >
                    {relatedContacts.length} {relatedContacts.length === 1 ? "Contact" : "Contacts"}
                  </Badge>
                )}
              </div>

              {relatedContacts.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-[120px] overflow-y-auto pr-1">
                  {relatedContacts.map((contact, index) => (
                    <div key={index} className="flex items-center p-2 bg-gray-800 rounded-md">
                      <Users className="h-3.5 w-3.5 mr-2 text-gray-400 flex-shrink-0" />
                      <div className="min-w-0">
                        <p className="text-sm font-medium truncate">{contact.name}</p>
                        <p className="text-xs text-gray-400 truncate capitalize">
                          {contact.relationshipType}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-400">{dictionary.review.noRelatedContacts}</p>
              )}
            </div>

            {/* Family Availability Card */}
            <div className="bg-gray-900 rounded-lg border border-gray-800 p-4">
              <div className="flex justify-between items-center border-b border-gray-800 pb-2 mb-3">
                <h4 className="text-sm font-medium">{dictionary.review.familyAvailability}</h4>
                {data.contact_availability && (
                  <Badge
                    variant="outline"
                    className="bg-primary/20 text-xs border-primary/50 text-primary-foreground"
                  >
                    {dictionary.review.complete}
                  </Badge>
                )}
              </div>

              <div className="space-y-2">
                {data.contact_availability ? (
                  <div>
                    {(() => {
                      // Parse availability data
                      const availabilityData = JSON.parse(data.contact_availability || "{}");
                      const contactIds = Object.keys(availabilityData);

                      if (contactIds.length === 0) {
                        return (
                          <div className="text-center p-2 bg-gray-800 rounded-md">
                            <p className="text-sm text-gray-400">
                              Aucune information de disponibilité fournie
                            </p>
                          </div>
                        );
                      }

                      // Count total available slots per contact
                      const contactAvailabilityCounts = contactIds.reduce(
                        (acc, contactId) => {
                          const availableSlots = availabilityData[contactId].filter(
                            (slot: any) => slot.available
                          ).length;
                          acc[contactId] = availableSlots;
                          return acc;
                        },
                        {} as Record<string, number>
                      );

                      // Get day names for display
                      const dayNames = ["Dim", "Lun", "Mar", "Mer", "Jeu", "Ven", "Sam"];

                      // Get time periods for display
                      const timePeriods: Record<string, string> = {
                        "9": "Matin",
                        "12": "Midi",
                        "15": "Après-midi",
                        "18": "Soirée",
                      };

                      // Create a summary of availability by day and time period
                      const availabilitySummary = contactIds.reduce((acc, contactId) => {
                        const contact = relatedContacts.find((c: any) => c.id === contactId);
                        if (!contact) return acc;

                        // Get available slots for this contact
                        const slots = availabilityData[contactId].filter(
                          (slot: any) => slot.available
                        );

                        // Group by day
                        const dayGroups = slots.reduce((days: any, slot: any) => {
                          const day = slot.day;
                          if (!days[day]) days[day] = [];
                          days[day].push(slot.hour);
                          return days;
                        }, {});

                        // Create summary text
                        const summary = Object.keys(dayGroups).map((day) => {
                          const dayName = dayNames[parseInt(day)];
                          const timeTexts = Object.keys(timePeriods)
                            .filter((hour) => dayGroups[day].includes(parseInt(hour)))
                            .map((hour) => timePeriods[hour as keyof typeof timePeriods]);

                          return `${dayName} (${timeTexts.join(", ")})`;
                        });

                        acc.push({
                          id: contactId,
                          name: contact.name,
                          relationshipType: contact.relationshipType,
                          availableCount: contactAvailabilityCounts[contactId],
                          summary: summary,
                        });

                        return acc;
                      }, [] as any[]);

                      return (
                        <div className="space-y-2">
                          {availabilitySummary.map((item, index) => (
                            <div key={index} className="p-2 bg-gray-800 rounded-md">
                              <div className="flex justify-between items-center mb-1">
                                <p className="text-sm font-medium">{item.name}</p>
                                <span className="text-xs text-primary/80 px-2 py-0.5 bg-primary/10 rounded-full">
                                  {item.availableCount} plages horaires
                                </span>
                              </div>
                              <p className="text-xs text-gray-400">{item.relationshipType}</p>
                              {item.summary.length > 0 ? (
                                <div className="mt-2 text-xs">
                                  <p className="text-gray-400 mb-1">Disponible:</p>
                                  <div className="flex flex-wrap gap-1">
                                    {item.summary.map((day: string, i: number) => (
                                      <span
                                        key={i}
                                        className="px-1.5 py-0.5 bg-gray-700 rounded text-xs"
                                      >
                                        {day}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              ) : (
                                <p className="mt-1 text-xs text-gray-500">
                                  Aucune disponibilité définie
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      );
                    })()}
                  </div>
                ) : (
                  <div className="text-center p-2 bg-gray-800 rounded-md">
                    <p className="text-sm text-gray-400">
                      Aucune information de disponibilité fournie
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
