# Simplified i18n with React Cache

This document explains the simplified approach to internationalization (i18n) using <PERSON>act's `cache` function in our Next.js 15 application with React Server Components.

## Overview

We've simplified the i18n implementation by:

1. Replacing the class-based `I18nService` with a simpler object that uses <PERSON><PERSON>'s `cache` function
2. Creating a smart `getDictionary()` function that automatically determines the appropriate language
3. Eliminating the need to pass language parameters between components

## Key Files

- `src/lib/i18n/services/I18nService.ts` - Simplified service using <PERSON>act's `cache`
- `src/lib/i18n/cache.ts` - Smart dictionary retrieval with language detection
- `src/lib/i18n/settings.ts` - Configuration for supported locales

## How It Works

### Language Resolution Priority

The `getDictionary()` function in `cache.ts` determines the language to use in this order:

1. **User Profile Setting** - For authenticated users, their stored language preference
2. **URL Parameter** - The language in the URL path (e.g., `/en/protected/...`)
3. **Cookie** - The `preferred-language` cookie if set
4. **Default Locale** - Falls back to the default locale (French)

### Benefits of Using React Cache

1. **Request-Scoped Memoization** - The dictionary is fetched only once per request
2. **Shared Across Components** - All server components in the request share the same dictionary
3. **No Unnecessary Waterfalls** - Eliminates component rendering waterfalls from awaiting language params
4. **Simplified Components** - Components don't need to handle language determination

## Usage Examples

### In a Server Component

```typescript
import { getDictionary } from '@/lib/i18n/cache';

export default async function MyComponent() {
  // Get the dictionary with automatic language detection
  const dictionary = await getDictionary();
  
  return (
    <div>
      <h1>{dictionary.myFeature.title}</h1>
      <p>{dictionary.myFeature.description}</p>
    </div>
  );
}
```

### Getting Domain-Specific Dictionaries

```typescript
import { getDomainDictionary } from '@/lib/i18n/cache';

export default async function MyComponent() {
  // Get just the user domain dictionary
  const userDictionary = await getDomainDictionary('user');
  
  return (
    <div>
      <h1>{userDictionary.profile.title}</h1>
    </div>
  );
}
```

### Updating User Language Preference

```typescript
import { updateLanguagePreference } from '@/lib/i18n/cache';

// In a server action
export async function changeLanguage(formData: FormData) {
  const language = formData.get('language') as string;
  await updateLanguagePreference(language);
  // Redirect or return response...
}
```

## Migration Guide

To migrate existing components:

1. Replace imports:
   ```typescript
   // Old
   import { i18n } from '@/lib/i18n/services/I18nService';
   
   // New
   import { getDictionary } from '@/lib/i18n/cache';
   ```

2. Replace dictionary fetching:
   ```typescript
   // Old
   const { lang } = await params;
   const dictionary = i18n.getDictionary(lang);
   
   // New
   const dictionary = await getDictionary();
   ```

3. For components that need the language parameter for other purposes (like redirects):
   ```typescript
   // Still get the language from params when needed
   const { lang } = await params;
   
   // But use the cached dictionary
   const dictionary = await getDictionary();
   ```

## Technical Details

- The `cache` function from React ensures the expensive operation is only performed once per request
- Results are cached for the duration of the request only
- Each new request gets a fresh execution of the cached function
- This approach is fully compatible with React Server Components and Next.js 15
