"use server";

import { redirect } from "next/navigation";
import { logger } from "@/lib/logger/services/LoggerService";
import { dtoDraftData } from "../../lib/dto/dtoDraftData";
import { updateDraft } from "../drafts/update";
import { getWizardConfig } from "../../lib/config/wizardRegistry";
import { getCurrentLanguage, buildLanguagePath } from "./navigationUtils";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { ExecutionInsert } from "../../lib/types/execution";

/**
 * Submit the wizard and create an execution record
 * @param draftId The draft ID
 * @param formData The form data
 */
export async function submitStep(draftId: string, formData: FormData) {
  const { draft, updatedData } = await dtoDraftData(draftId, formData);

  // Get wizard configuration
  const wizardConfig = getWizardConfig(draft.workflow_type);
  const currentStep = wizardConfig.stepsMap.get(draft.current_step);

  if (!currentStep) {
    throw new Error(`Step not found in configuration: ${draft.current_step}`);
  }

  if (!currentStep.isFinalStep) {
    logger.warn(`Attempting to submit a non-final step: ${draft.current_step}`);
  }

  // Update draft
  await updateDraft(draftId, updatedData, "submitted");

  // Create execution record
  const supabase = await createClient();
  const user = await auth.getCurrentUser();
  const organizationId = await auth.getCurrentUserOrganizationId();

  if (!user || !organizationId) {
    throw new Error("User or organization not found");
  }

  const execution: ExecutionInsert = {
    user_id: user.id,
    organization_id: organizationId,
    workflow_type: draft.workflow_type,
    status: "pending",
    data: draft.data,
  };

  const { data, error } = await supabase.from("executions").insert(execution).select().single();

  if (error) {
    logger.error(`Error creating execution: ${error.message}`);
    throw new Error(`Failed to create execution: ${error.message}`);
  }

  const executionId = data.id;
  logger.info(`Created execution record: ${executionId}`);

  // Create a notification about the workflow submission
  try {
    const notification = {
      user_id: user.id,
      organization_id: organizationId,
      type: "info",
      title: "Workflow Submitted",
      message: `Your ${draft.workflow_type} has been submitted and is being processed.`,
      data: {
        executionId: data.id,
        draftId: draft.id,
        workflowType: draft.workflow_type,
      },
      read: false,
    };

    const { error: notificationError } = await supabase.from("notifications").insert(notification);

    if (notificationError) {
      logger.error(`Error creating notification: ${notificationError.message}`);
    }
  } catch (notificationError) {
    logger.error(`Error creating notification: ${notificationError}`);
  }

  // Get current language
  const lang = await getCurrentLanguage();

  // Redirect to execution status page
  const url = await buildLanguagePath(`/protected/automation/execution/${executionId}`, lang);
  redirect(url);
}
