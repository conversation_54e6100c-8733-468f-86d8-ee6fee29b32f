import { notFound } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, Home, Filter } from "lucide-react";
import Link from "next/link";
import { i18n } from "@/lib/i18n/services/I18nService";
import { RequestHistoryTimeline } from "../../../components/RequestHistoryTimeline";
import { getRequestWithAllRelations, getRequestHistory } from "../../../actions";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import DOMAIN_CONFIG from "../../../lib/config/domain";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface HistoryPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
  searchParams: Promise<{
    action?: string;
    from_date?: string;
    to_date?: string;
    user_id?: string;
  }>;
}

/**
 * History page for a specific Request item
 * Displays a detailed timeline of all changes to the request
 */
export default async function HistoryPage({
  params,
  searchParams: searchParamPromise,
}: HistoryPageProps) {
  // Await the params
  const searchParams = await searchParamPromise;
  const { lang, id } = await params;

  // Get the dictionary
  const dictionary = i18n.getDictionary(lang);

  // Get the current user
  const user = await auth.getCurrentUser();
  if (!user) {
    notFound();
  }

  // Get the request data with all relations
  const response = await getRequestWithAllRelations(id);

  // If the request doesn't exist, show the not found page
  if (!response.success || !response.data) {
    notFound();
  }

  // Extract search parameters
  const action = searchParams.action;
  const fromDate = searchParams.from_date;
  const toDate = searchParams.to_date;
  const userId = searchParams.user_id;

  // Get the request history with filters
  const historyResponse = await getRequestHistory({
    requestId: id,
    action,
    fromDate,
    toDate,
    userId,
    includeUserDetails: true,
    sortOrder: "desc",
  });

  // Get the history items
  const historyItems =
    historyResponse.success && historyResponse.data ? historyResponse.data.items : [];

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${lang}/protected/dashboard`}>
              <Home className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${lang}${DOMAIN_CONFIG.basePath}/list`}>
              Requests
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${lang}${DOMAIN_CONFIG.basePath}/${id}`}>View</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>History</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back button */}
      <div>
        <Button variant="outline" size="sm" asChild className="mb-4">
          <Link href={`/${lang}${DOMAIN_CONFIG.basePath}/${id}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Request
          </Link>
        </Button>
      </div>

      {/* History filters */}
      <Card>
        <CardHeader>
          <CardTitle>History Filters</CardTitle>
          <CardDescription>
            Filter the history timeline by action type, date range, or user
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label htmlFor="action" className="text-sm font-medium">
                  Action Type
                </label>
                <Select name="action" defaultValue={action}>
                  <SelectTrigger>
                    <SelectValue placeholder="All actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All actions</SelectItem>
                    <SelectItem value="created">Created</SelectItem>
                    <SelectItem value="updated">Updated</SelectItem>
                    <SelectItem value="status_changed">Status Changed</SelectItem>
                    <SelectItem value="commented">Commented</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label htmlFor="from_date" className="text-sm font-medium">
                  From Date
                </label>
                <Input type="date" name="from_date" defaultValue={fromDate} />
              </div>

              <div className="space-y-2">
                <label htmlFor="to_date" className="text-sm font-medium">
                  To Date
                </label>
                <Input type="date" name="to_date" defaultValue={toDate} />
              </div>

              <div className="flex items-end">
                <Button type="submit" className="w-full">
                  <Filter className="mr-2 h-4 w-4" />
                  Apply Filters
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* History timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Request History</CardTitle>
          <CardDescription>Complete timeline of changes to this request</CardDescription>
        </CardHeader>
        <CardContent>
          {historyItems.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No history entries found for this request.
            </div>
          ) : (
            <RequestHistoryTimeline history={historyItems} dictionary={dictionary} />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
