import { logger } from "@/lib/logger/services/LoggerService";
import { defaultLocale, Locale, locales } from "@/lib/i18n/settings";
import { cache } from "react";

// Direct imports of JSON files
import en from "../locales/en.json";
import fr from "../locales/fr.json";

// Create a type-safe dictionary mapping
const dictionaries = {
  en,
  fr,
} as const;

// Infer the Dictionary type from the actual structure
export type Dictionary = typeof en;

/**
 * Validates if a locale is supported
 * @param locale Locale to validate
 * @returns True if the locale is supported, false otherwise
 */
const isValidLocale = (locale: string): locale is Locale => {
  return locales.includes(locale as Locale);
};

/**
 * Gets a dictionary for a specific locale
 * Uses React's cache function to memoize results within a request
 */
const getDictionaryInternal = cache((locale: string): Dictionary => {
  // Validate locale and fallback to default if invalid
  const validLocale = isValidLocale(locale) ? locale : defaultLocale;

  try {
    // Get the dictionary for the locale
    const dictionary = dictionaries[validLocale as keyof typeof dictionaries];
    return dictionary;
  } catch (error) {
    logger.error(`Failed to load dictionary for locale: ${validLocale}`, error as Error);

    // Fallback to default locale if there's an error
    if (validLocale !== defaultLocale) {
      return getDictionaryInternal(defaultLocale);
    }

    // If default locale fails, return English dictionary as fallback
    return dictionaries.en;
  }
});

/**
 * Simplified i18n service using React's cache function
 */
export const i18n = {
  /**
   * Gets the list of available locales
   * @returns Array of available locales
   */
  getLocales: (): readonly Locale[] => locales,

  /**
   * Gets the default locale
   * @returns Default locale
   */
  getDefaultLocale: (): Locale => defaultLocale,

  /**
   * Validates if a locale is supported
   * @param locale Locale to validate
   * @returns True if the locale is supported, false otherwise
   */
  isValidLocale,

  /**
   * Gets a dictionary for a specific locale
   * @param locale Locale to get dictionary for
   * @returns The dictionary
   */
  getDictionary: (locale: string): Dictionary => {
    return getDictionaryInternal(locale);
  },

  /**
   * Gets a domain-specific dictionary for a specific locale
   * @param locale Locale to get dictionary for
   * @param domain Domain to get dictionary for
   * @returns The domain-specific dictionary
   */
  getDomainDictionary: <K extends keyof Dictionary>(locale: string, domain: K): Dictionary[K] => {
    const dictionary = getDictionaryInternal(locale);
    return dictionary[domain];
  },

  /**
   * Gets a domain and feature-specific dictionary for a specific locale
   * @param locale Locale to get dictionary for
   * @param domain Domain to get dictionary for
   * @param feature Feature to get dictionary for
   * @returns The domain and feature-specific dictionary
   */
  getDomainFeatureDictionary: <K extends keyof Dictionary, F extends keyof Dictionary[K]>(
    locale: string,
    domain: K,
    feature: F
  ): Dictionary[K][F] => {
    const dictionary = getDictionaryInternal(locale);
    return dictionary[domain][feature];
  },
};

/**
 * Helper function to get a dictionary for a specific locale
 * @param locale Locale to get dictionary for
 * @returns The dictionary
 */
export const getDictionary = cache(async (locale: string): Promise<Dictionary> => {
  return i18n.getDictionary(locale);
});
