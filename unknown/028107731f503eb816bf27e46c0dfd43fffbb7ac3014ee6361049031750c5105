import { <PERSON>, Json } from "@/lib/types/database.types";

/**
 * Execution types directly from database schema
 */
export type Execution = Database["public"]["Tables"]["executions"]["Row"];
export type ExecutionInsert = Database["public"]["Tables"]["executions"]["Insert"];
export type ExecutionUpdate = Database["public"]["Tables"]["executions"]["Update"];

/**
 * Generic execution types with type safety for data and result
 */
export type TypedExecution<TData = Json, TResult = Json> = Omit<Execution, "data" | "result"> & {
  data: TData;
  result: TResult;
};

export type TypedExecutionInsert<TData = Json, TResult = Json> = Omit<
  ExecutionInsert,
  "data" | "result"
> & {
  data: TData;
  result?: TResult;
};

export type TypedExecutionUpdate<TData = Json, TResult = Json> = Omit<
  ExecutionUpdate,
  "data" | "result"
> & {
  data?: TData;
  result?: TResult;
};

/**
 * Execution status types
 */
export type ExecutionStatus = "pending" | "running" | "completed" | "failed" | "cancelled";
