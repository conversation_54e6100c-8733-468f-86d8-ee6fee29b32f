{"name": "Status Error <PERSON> (Fixed)", "nodes": [{"parameters": {}, "id": "node_1", "name": "When Called From Another Workflow", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [0, 300], "typeVersion": 1}, {"parameters": {"operation": "update", "tableId": "executions", "filters": {"conditions": [{"keyName": "id", "condition": "eq", "keyValue": "={{ $json.executionId }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "status", "fieldValue": "failed"}, {"fieldId": "error", "fieldValue": "={{ $json.error }}"}, {"fieldId": "completed_at", "fieldValue": "={{ $now.toISOString() }}"}, {"fieldId": "updated_at", "fieldValue": "={{ $now.toISOString() }}"}]}}, "id": "node_2", "name": "Update Status to Failed", "type": "n8n-nodes-base.supabase", "position": [200, 300], "typeVersion": 1, "credentials": {"supabaseApi": {"id": "jrODeHKmkXcRzjY7", "name": "Supabase Local"}}}], "pinData": {"When Called From Another Workflow": [{"json": {"executionId": "5d2d548e-8c35-4ce2-a965-fa71552e3d17", "result": {"job_title": "freerfer", "last_name": "reerfer", "first_name": "erfe", "employment_status": "active", "organization_id": "00000000-0000-0000-0000-000000000001", "emails": [{"type": "work", "email": "<EMAIL>", "primary": true}], "dog": true, "error": "Could not find the 'dog' column of 'employees' in the schema cache"}, "title": "Employee Creation", "message": "Could not find the 'dog' column of 'employees' in the schema cache", "user_id": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "organization_id": "00000000-0000-0000-0000-000000000001"}}]}, "connections": {"When Called From Another Workflow": {"main": [[{"node": "Update Status to Failed", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "963f937f-675e-4ca5-b26c-c2d70967d295", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "L6MfiSfamU9NnZho", "tags": [{"createdAt": "2025-05-19T01:18:30.835Z", "updatedAt": "2025-05-19T01:18:30.835Z", "id": "Dnsl9yLb11FuJixk", "name": "Execution"}]}