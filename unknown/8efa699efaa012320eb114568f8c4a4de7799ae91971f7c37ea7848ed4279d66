"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { Card, CardContent } from "@/components/ui/card";
import { H2, P } from "@/components/typography";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Loader2, CheckCircle, XCircle, Clock, PlayCircle } from "lucide-react";
import { motion } from "framer-motion";
import { Execution } from "../../../lib/types";

/**
 * Execution Status Page
 * Displays the current status of a workflow execution
 */
export default function ExecutionStatusPage() {
  const params = useParams();
  const executionId = params.executionId as string;
  const [execution, setExecution] = useState<Execution | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch execution data
  useEffect(() => {
    const fetchExecution = async () => {
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from("executions")
          .select("*")
          .eq("id", executionId)
          .single();

        if (error) {
          throw error;
        }

        setExecution(data);
      } catch (err) {
        setError("Failed to load execution data");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchExecution();
  }, [executionId]);

  // Subscribe to execution updates
  useEffect(() => {
    const supabase = createClient();

    // Subscribe to changes
    const channel = supabase
      .channel(`execution-${executionId}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "executions",
          filter: `id=eq.${executionId}`,
        },
        (payload) => {
          setExecution(payload.new as Execution);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [executionId]);

  // Render loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Loader2 className="h-16 w-16 text-primary animate-spin mb-4" />
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <H2>Loading execution status...</H2>
        </motion.div>
      </div>
    );
  }

  // Render error state
  if (error || !execution) {
    // Get the current language from the URL
    const lang = typeof window !== "undefined" ? window.location.pathname.split("/")[1] : "en";

    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
          animate={{ opacity: 1, scale: 1, rotate: 0 }}
          transition={{
            type: "spring",
            stiffness: 260,
            damping: 20,
            delay: 0.3,
          }}
        >
          <XCircle className="h-16 w-16 text-destructive mb-4" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.5 }}
        >
          <H2>Error Loading Execution</H2>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.9, duration: 0.5 }}
        >
          <P className="text-muted-foreground mb-8">{error || "Execution not found"}</P>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2, duration: 0.5 }}
        >
          <Button asChild>
            <Link href={`/${lang}/protected/employee/management/list`}>Back to Employee</Link>
          </Button>
        </motion.div>
      </div>
    );
  }

  // Render status icon based on execution status
  const renderStatusIcon = () => {
    switch (execution.status) {
      case "pending":
        return (
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{
              repeat: Infinity,
              duration: 2,
              delay: 1.5, // Add delay for fluidity
            }}
          >
            <Clock className="h-24 w-24 text-amber-500" />
          </motion.div>
        );
      case "running":
        return (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{
              repeat: Infinity,
              duration: 2,
              ease: "linear",
              delay: 1.5, // Add delay for fluidity
            }}
          >
            <PlayCircle className="h-24 w-24 text-blue-500" />
          </motion.div>
        );
      case "completed":
        return (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: 1.5, // Add delay for fluidity
            }}
          >
            <CheckCircle className="h-24 w-24 text-green-500" />
          </motion.div>
        );
      case "failed":
        return (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: 1.5, // Add delay for fluidity
            }}
          >
            <XCircle className="h-24 w-24 text-destructive" />
          </motion.div>
        );
      default:
        return <Clock className="h-24 w-24 text-muted-foreground" />;
    }
  };

  // Get status text
  const getStatusText = () => {
    switch (execution.status) {
      case "pending":
        return "Waiting to start";
      case "running":
        return "Processing";
      case "completed":
        return "Completed successfully";
      case "failed":
        return "Failed";
      case "cancelled":
        return "Cancelled";
      default:
        return "Unknown status";
    }
  };

  // Get the current language from the URL
  const lang = typeof window !== "undefined" ? window.location.pathname.split("/")[1] : "en";

  return (
    <Card className="max-w-2xl mx-auto">
      <CardContent className="pt-6 flex flex-col items-center text-center">
        <div className="mb-6">{renderStatusIcon()}</div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <H2 className="mb-2">{execution.workflow_type.replace(/_/g, " ")}</H2>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.0, duration: 0.8 }}
        >
          <P className="text-xl font-medium mb-2">{getStatusText()}</P>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.8 }}
        >
          <P className="text-muted-foreground mb-6">
            {execution.status === "failed" && execution.error
              ? `Error: ${execution.error}`
              : execution.status === "completed"
                ? "Your request has been processed successfully."
                : "Your request is being processed. This page will update automatically."}
          </P>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2.0, duration: 0.5 }}
          className="flex gap-4 mt-4"
        >
          <Button variant="outline" asChild>
            <Link href={`/${lang}/protected/employee/management/list`}>Back to Employee</Link>
          </Button>
        </motion.div>
      </CardContent>
    </Card>
  );
}
