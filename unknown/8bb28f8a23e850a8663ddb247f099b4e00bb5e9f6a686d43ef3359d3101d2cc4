{"name": "Workflow Router", "nodes": [{"parameters": {"httpMethod": "POST", "path": "4db68191-446b-4b5f-bd5d-d9ef9b5de254", "options": {}}, "id": "d617d309-6c27-4187-89c5-a343d2740ad4", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [-900, 100], "typeVersion": 1, "webhookId": "4db68191-446b-4b5f-bd5d-d9ef9b5de254"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.workflow_type }}", "rightValue": "employee_creation", "operator": {"type": "string", "operation": "contains"}, "id": "898cac8d-2c5c-4fd7-83d3-bd7d1628da22"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Employee Creation"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a52ac15b-5c9b-458f-9d7f-9eaeddd7aac0", "leftValue": "={{ $json.workflow_type }}", "rightValue": "request_creation", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "Request Creation"}]}, "options": {}}, "id": "63e1ea15-098f-451b-b7fc-0b6ea2de0bae", "name": "Switch", "type": "n8n-nodes-base.switch", "position": [-460, 100], "typeVersion": 3.2}, {"parameters": {"mode": "raw", "jsonOutput": "=\n{\n\"executionId\": \"{{ $('Webhook Trigger').first().json.body.id }}\",\n\"result\": {{ $json.result.toJsonString() }},\n\"title\": \"{{ $json.notification.title }}\",\n\"message\": \"{{ $json.notification.message }}\",\n\"user_id\": \"{{ $('Webhook Trigger').first().json.body.user_id }}\",\n\"organization_id\": \"{{ $('Webhook Trigger').first().json.body.organization_id}}\"\n}\n", "options": {}}, "id": "306ceb85-93fc-4f7a-a182-ff7548908b84", "name": "<PERSON>", "type": "n8n-nodes-base.set", "position": [-20, 100], "typeVersion": 3.4}, {"parameters": {"workflowId": "ExJiiEtCksRpM0du", "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-680, 100], "id": "82a63f83-1161-4332-8bfa-64baac30724a", "name": "Execute Workflow"}, {"parameters": {"workflowId": "zcFVBb9uIXIT9Bnc", "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-240, 0], "id": "0603ffd9-df5a-47d4-aaac-cd5fe0b88641", "name": "Employee Creation"}, {"parameters": {"workflowId": "sl2kc45FXNqr4qOm", "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-240, 200], "id": "050c1abc-faf4-4a4d-98e8-6e48508d1e1d", "name": "Request Creation (Enhanced)"}, {"parameters": {"workflowId": "dxlrwZJcwvFimlOG", "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [200, 0], "id": "6f6f3d05-fa64-4007-917d-ee641c738bfa", "name": "Notification Success"}, {"parameters": {"workflowId": "O0LORCEXJeFs8F84", "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [200, 200], "id": "58d3c002-b170-4a83-a511-c871ad46dd6c", "name": "Complete Execution"}], "connections": {"Webhook Trigger": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Employee Creation", "type": "main", "index": 0}], [{"node": "Request Creation (Enhanced)", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Notification Success", "type": "main", "index": 0}, {"node": "Complete Execution", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Employee Creation": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Request Creation (Enhanced)": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "tags": [{"name": "Entry Point"}]}