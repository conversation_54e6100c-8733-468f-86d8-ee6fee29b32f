{"name": "Workflow Router", "nodes": [{"parameters": {"httpMethod": "POST", "path": "4db68191-446b-4b5f-bd5d-d9ef9b5de254", "options": {}}, "id": "ec784175-92e7-48b8-a108-904dbe335ba4", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "position": [-200, 540], "typeVersion": 1, "webhookId": "4db68191-446b-4b5f-bd5d-d9ef9b5de254"}, {"parameters": {"workflowId": "O0LORCEXJeFs8F84", "options": {}}, "id": "096880a2-f5f7-4963-b315-cb9da070f60a", "name": "Complete Execution", "type": "n8n-nodes-base.executeWorkflow", "position": [1180, 480], "typeVersion": 1}, {"parameters": {"workflowId": {"__rl": true, "value": "dxlrwZJcwvFimlOG", "mode": "list", "cachedResultName": "Notification Success"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [1180, 140], "id": "f44cb791-cbac-4b39-ba47-c2a9e30ab319", "name": "Notification Success"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.body.workflow_type }}", "rightValue": "employee_creation", "operator": {"type": "string", "operation": "contains"}, "id": "898cac8d-2c5c-4fd7-83d3-bd7d1628da22"}], "combinator": "and"}, "renameOutput": true, "outputKey": "Employee Creation"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [200, 240], "id": "513408fb-605d-4f38-ac3a-d34b215e3d15", "name": "Switch"}, {"parameters": {"workflowId": {"__rl": true, "value": "zcFVBb9uIXIT9Bnc", "mode": "list", "cachedResultName": "Employee Creation"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [480, -80], "id": "ff966e57-573f-4fb5-bb5b-808f209d74c2", "name": "Employee Creation"}, {"parameters": {"mode": "raw", "jsonOutput": "=\n{\n\"executionId\": \"{{ $('Webhook Trigger').first().json.body.id }}\",\n\"result\": {{ $json.result.toJsonString() }},\n\"title\": \"{{ $json.notification.tile }}\",\n\"message\": \"{{ $json.notification.message }}\",\n\"user_id\": \"{{ $('Webhook Trigger').first().json.body.user_id }}\",\n\"organization_id\": \"{{ $('Webhook Trigger').first().json.body.organization_id}}\"\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [820, 280], "id": "14eeab07-3a7d-4af0-a7e4-9a94ece7744b", "name": "<PERSON>"}], "pinData": {"Webhook Trigger": [{"json": {"headers": {"host": "host.docker.internal:5678", "user-agent": "PostgreSQL 15.8 on x86_64-pc-linux-gnu, compiled by gcc (GCC) 13.2.0, 64-bit", "accept": "*/*", "accept-encoding": "deflate, gzip, br, zstd", "connection": "close", "charsets": "utf-8", "content-type": "application/json", "content-length": "651"}, "params": {}, "query": {}, "body": {"id": "e0f96300-889b-4505-980b-d7d938823463", "data": {"basicInfo": {"job_title": "ddd", "last_name": "ddd", "department": "ddddd", "first_name": "ddddd"}, "contactInfo": {"emails": [{"type": "work", "email": "<EMAIL>", "primary": true}], "phones": [{"type": "work", "number": "", "primary": true}], "address": ""}, "employmentDetails": {"hire_date": "", "employee_id": "", "supervisor_id": "", "employment_status": "active"}}, "status": "pending", "user_id": "b2fd2101-cd35-4bb5-8453-9ed0e481f5bd", "created_at": "2025-05-19T08:50:58.294656+00:00", "workflow_type": "employee_creation", "organization_id": "00000000-0000-0000-0000-000000000001"}, "webhookUrl": "http://localhost:5678/webhook/4db68191-446b-4b5f-bd5d-d9ef9b5de254", "executionMode": "production"}}]}, "connections": {"Webhook Trigger": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Complete Execution": {"main": [[]]}, "Notification Success": {"main": [[]]}, "Switch": {"main": [[{"node": "Employee Creation", "type": "main", "index": 0}]]}, "Employee Creation": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Complete Execution", "type": "main", "index": 0}, {"node": "Notification Success", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "RF8aJyM6Ga2DjddH"}, "versionId": "e6d9b3d6-8a81-4efa-a5b2-1c1bf57ae8e7", "meta": {"templateCredsSetupCompleted": true, "instanceId": "418cc5be192642578ca63fced2f4877a1bd4fbb73bcf7d5a4634e29e38c8a97a"}, "id": "aUkGPcC05hOBNg7B", "tags": [{"createdAt": "2025-05-19T05:45:21.998Z", "updatedAt": "2025-05-19T05:45:21.998Z", "id": "UkYDhzq4vf2KKR9d", "name": "Entry Point"}]}