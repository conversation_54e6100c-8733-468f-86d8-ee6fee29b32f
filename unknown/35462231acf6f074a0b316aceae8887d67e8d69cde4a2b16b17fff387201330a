-- Create Scheduling Domain Tables
-- This migration creates the appointment management system with intelligent request evaluation,
-- resource management, and multi-calendar support.

-- Appointment status enum
CREATE TYPE appointment_status AS ENUM ('planned', 'confirmed', 'in_progress', 'completed', 'missed', 'postponed', 'cancelled');

-- Main appointments table
CREATE TABLE appointments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

  -- Flexible relationships
  case_file_id UUID REFERENCES case_files(id), -- NULL for non-case appointments
  service_id UUID REFERENCES services(id),

  -- Appointment details
  title TEXT NOT NULL,
  description TEXT,
  appointment_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  duration_minutes INTEGER NOT NULL,

  -- Status and management
  status appointment_status NOT NULL DEFAULT 'planned',
  created_by UUID NOT NULL REFERENCES auth.users(id),

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  confirmed_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,

  -- Calendar context
  family_availability JSONB, -- For case file calendar integration

  -- Flexible metadata
  metadata JSONB DEFAULT '{}',

  -- Constraints
  CONSTRAINT fk_appointments_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_appointments_case_file FOREIGN KEY (case_file_id) REFERENCES case_files(id),
  CONSTRAINT fk_appointments_service FOREIGN KEY (service_id) REFERENCES services(id),
  CONSTRAINT fk_appointments_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id)
);

-- Appointment participants table
CREATE TABLE appointment_participants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  contact_id UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
  participation_type TEXT DEFAULT 'attendee', -- 'attendee', 'observer', 'guardian'
  attendance_status TEXT DEFAULT 'invited', -- 'invited', 'confirmed', 'attended', 'no_show'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Constraints
  CONSTRAINT fk_appointment_participants_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_appointment_participants_appointment FOREIGN KEY (appointment_id) REFERENCES appointments(id),
  CONSTRAINT fk_appointment_participants_contact FOREIGN KEY (contact_id) REFERENCES contacts(id),
  UNIQUE(appointment_id, contact_id, participation_type)
);

-- Employee assignments table
CREATE TABLE appointment_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
  assignment_role TEXT DEFAULT 'primary', -- 'primary', 'secondary', 'observer'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Constraints
  CONSTRAINT fk_appointment_assignments_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_appointment_assignments_appointment FOREIGN KEY (appointment_id) REFERENCES appointments(id),
  CONSTRAINT fk_appointment_assignments_employee FOREIGN KEY (employee_id) REFERENCES employees(id),
  UNIQUE(appointment_id, employee_id, assignment_role)
);

-- Room reservations table
CREATE TABLE appointment_rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  room_id UUID NOT NULL REFERENCES rooms(id) ON DELETE CASCADE,
  reserved_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  reserved_by UUID NOT NULL REFERENCES auth.users(id),

  -- Constraints
  CONSTRAINT fk_appointment_rooms_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_appointment_rooms_appointment FOREIGN KEY (appointment_id) REFERENCES appointments(id),
  CONSTRAINT fk_appointment_rooms_room FOREIGN KEY (room_id) REFERENCES rooms(id),
  CONSTRAINT fk_appointment_rooms_reserved_by FOREIGN KEY (reserved_by) REFERENCES auth.users(id),
  UNIQUE(appointment_id, room_id)
);

-- Room availability table (aligned with employee availability pattern)
CREATE TABLE room_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id UUID REFERENCES rooms(id) NOT NULL,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  day_of_week INTEGER NOT NULL CHECK (day_of_week BETWEEN 0 AND 6),
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_recurring BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),

  -- Constraints
  CONSTRAINT fk_room_availability_room FOREIGN KEY (room_id) REFERENCES rooms(id),
  CONSTRAINT fk_room_availability_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_room_availability_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id)
);

-- Room availability exceptions table
CREATE TABLE room_availability_exceptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  room_id UUID REFERENCES rooms(id) NOT NULL,
  organization_id UUID REFERENCES organizations(id) NOT NULL,
  exception_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN NOT NULL DEFAULT FALSE,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_by UUID REFERENCES auth.users(id),

  -- Constraints
  CONSTRAINT fk_room_availability_exceptions_room FOREIGN KEY (room_id) REFERENCES rooms(id),
  CONSTRAINT fk_room_availability_exceptions_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_room_availability_exceptions_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id)
);

-- CRITICAL: Request capacity analysis table (intelligent request evaluation)
CREATE TABLE appointment_capacity_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  request_id UUID REFERENCES requests(id), -- Link to request being evaluated

  -- Service requirements
  service_id UUID NOT NULL REFERENCES services(id),
  total_appointments_needed INTEGER NOT NULL,
  frequency_pattern TEXT NOT NULL, -- 'monthly', 'weekly', 'bi-weekly'
  service_start_date DATE NOT NULL,
  service_end_date DATE NOT NULL,

  -- Family constraints
  family_availability JSONB NOT NULL, -- Copy from request_metadata
  required_duration_minutes INTEGER NOT NULL,

  -- Capacity analysis results
  analysis_status TEXT NOT NULL, -- 'feasible', 'waitlist', 'impossible'
  available_slots_found INTEGER NOT NULL DEFAULT 0,
  first_available_slot DATE,
  last_available_slot DATE,

  -- Detailed analysis
  slot_analysis JSONB, -- Detailed breakdown of available slots
  capacity_conflicts JSONB, -- What's blocking full capacity

  -- Analysis metadata
  analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  analyzed_by UUID REFERENCES auth.users(id),

  -- Recommendations
  recommended_action TEXT, -- 'approve', 'waitlist', 'reject', 'modify_schedule'
  alternative_options JSONB, -- Alternative scheduling options if primary fails

  -- Constraints
  CONSTRAINT fk_appointment_capacity_analysis_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_appointment_capacity_analysis_request FOREIGN KEY (request_id) REFERENCES requests(id),
  CONSTRAINT fk_appointment_capacity_analysis_service FOREIGN KEY (service_id) REFERENCES services(id),
  CONSTRAINT fk_appointment_capacity_analysis_analyzed_by FOREIGN KEY (analyzed_by) REFERENCES auth.users(id)
);

-- CRITICAL: Organization availability slots table (for fast capacity queries)
CREATE TABLE organization_availability_slots (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

  -- Slot details
  slot_date DATE NOT NULL,
  slot_day_of_week INTEGER NOT NULL, -- 0-6 for quick filtering
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  duration_minutes INTEGER NOT NULL,

  -- Availability status
  is_available BOOLEAN NOT NULL DEFAULT true,
  employee_id UUID REFERENCES employees(id), -- Which employee is available
  room_id UUID REFERENCES rooms(id), -- Which room is available

  -- Booking status
  is_booked BOOLEAN NOT NULL DEFAULT false,
  booked_by_appointment_id UUID REFERENCES appointments(id),

  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),

  -- Constraints
  CONSTRAINT fk_organization_availability_slots_organization FOREIGN KEY (organization_id) REFERENCES organizations(id),
  CONSTRAINT fk_organization_availability_slots_employee FOREIGN KEY (employee_id) REFERENCES employees(id),
  CONSTRAINT fk_organization_availability_slots_room FOREIGN KEY (room_id) REFERENCES rooms(id),
  CONSTRAINT fk_organization_availability_slots_appointment FOREIGN KEY (booked_by_appointment_id) REFERENCES appointments(id),
  UNIQUE(organization_id, slot_date, start_time, employee_id, room_id)
);

-- Create indexes for performance
CREATE INDEX idx_appointments_organization_id ON appointments(organization_id);
CREATE INDEX idx_appointments_case_file_id ON appointments(case_file_id);
CREATE INDEX idx_appointments_service_id ON appointments(service_id);
CREATE INDEX idx_appointments_status ON appointments(status);
CREATE INDEX idx_appointments_date ON appointments(appointment_date);
CREATE INDEX idx_appointments_created_by ON appointments(created_by);

CREATE INDEX idx_appointment_participants_organization_id ON appointment_participants(organization_id);
CREATE INDEX idx_appointment_participants_appointment_id ON appointment_participants(appointment_id);
CREATE INDEX idx_appointment_participants_contact_id ON appointment_participants(contact_id);

CREATE INDEX idx_appointment_assignments_organization_id ON appointment_assignments(organization_id);
CREATE INDEX idx_appointment_assignments_appointment_id ON appointment_assignments(appointment_id);
CREATE INDEX idx_appointment_assignments_employee_id ON appointment_assignments(employee_id);

CREATE INDEX idx_appointment_rooms_organization_id ON appointment_rooms(organization_id);
CREATE INDEX idx_appointment_rooms_appointment_id ON appointment_rooms(appointment_id);
CREATE INDEX idx_appointment_rooms_room_id ON appointment_rooms(room_id);

CREATE INDEX idx_room_availability_organization_id ON room_availability(organization_id);
CREATE INDEX idx_room_availability_room_id ON room_availability(room_id);
CREATE INDEX idx_room_availability_day_of_week ON room_availability(day_of_week);

CREATE INDEX idx_room_availability_exceptions_organization_id ON room_availability_exceptions(organization_id);
CREATE INDEX idx_room_availability_exceptions_room_id ON room_availability_exceptions(room_id);
CREATE INDEX idx_room_availability_exceptions_date ON room_availability_exceptions(exception_date);

CREATE INDEX idx_appointment_capacity_analysis_organization_id ON appointment_capacity_analysis(organization_id);
CREATE INDEX idx_appointment_capacity_analysis_request_id ON appointment_capacity_analysis(request_id);
CREATE INDEX idx_appointment_capacity_analysis_service_id ON appointment_capacity_analysis(service_id);
CREATE INDEX idx_appointment_capacity_analysis_status ON appointment_capacity_analysis(analysis_status);

CREATE INDEX idx_organization_availability_slots_organization_id ON organization_availability_slots(organization_id);
CREATE INDEX idx_organization_availability_slots_date ON organization_availability_slots(slot_date);
CREATE INDEX idx_organization_availability_slots_day_of_week ON organization_availability_slots(slot_day_of_week);
CREATE INDEX idx_organization_availability_slots_employee_id ON organization_availability_slots(employee_id);
CREATE INDEX idx_organization_availability_slots_room_id ON organization_availability_slots(room_id);
CREATE INDEX idx_organization_availability_slots_available ON organization_availability_slots(is_available);
CREATE INDEX idx_organization_availability_slots_booked ON organization_availability_slots(is_booked);

-- Create triggers for updated_at
CREATE TRIGGER update_appointments_updated_at
BEFORE UPDATE ON appointments
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_room_availability_updated_at
BEFORE UPDATE ON room_availability
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_organization_availability_slots_updated_at
BEFORE UPDATE ON organization_availability_slots
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE appointments IS 'Main appointments table supporting case file and standalone appointments';
COMMENT ON TABLE appointment_participants IS 'Contacts participating in appointments with attendance tracking';
COMMENT ON TABLE appointment_assignments IS 'Employee assignments to appointments with role definitions';
COMMENT ON TABLE appointment_rooms IS 'Room reservations for appointments';
COMMENT ON TABLE room_availability IS 'Room availability schedule following employee availability pattern';
COMMENT ON TABLE room_availability_exceptions IS 'Room availability exceptions and overrides';
COMMENT ON TABLE appointment_capacity_analysis IS 'CRITICAL: Intelligent request evaluation for capacity planning';
COMMENT ON TABLE organization_availability_slots IS 'CRITICAL: Pre-calculated availability slots for fast capacity queries';

COMMENT ON COLUMN appointments.family_availability IS 'JSONB copy of family availability from request_metadata for calendar integration';
COMMENT ON COLUMN appointment_capacity_analysis.analysis_status IS 'Result of capacity analysis: feasible, waitlist, or impossible';
COMMENT ON COLUMN organization_availability_slots.slot_day_of_week IS '0=Sunday, 1=Monday, ..., 6=Saturday for quick filtering';