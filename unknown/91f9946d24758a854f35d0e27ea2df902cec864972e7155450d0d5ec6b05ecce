"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useActionState } from "react";
import { Request, RequestStatus } from "../lib/types";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { CheckCircle, Clock, ClipboardList, XCircle, Loader2, AlertTriangle } from "lucide-react";
import { updateRequestStatus } from "../actions/update";
import { ActionState } from "@/lib/types/responses";
import { useRouter } from "next/navigation";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface RequestStatusActionsProps {
  /**
   * The request to display actions for
   */
  request: Request;

  /**
   * Dictionary for translations
   */
  dictionary: any;

  /**
   * The current user's permissions
   */
  permissions: {
    canApprove: boolean;
    canReject: boolean;
    canComplete: boolean;
    canWaitlist: boolean;
    canRequest: boolean;
  };

  /**
   * The language code
   */
  lang: string;
}

/**
 * RequestStatusActions component
 * Displays appropriate action buttons based on the current status of a request
 */
export function RequestStatusActions({
  request,
  dictionary,
  permissions,
  lang,
}: RequestStatusActionsProps) {
  const router = useRouter();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);
  const [notes, setNotes] = useState("");

  // Initial state for the action
  const initialState: ActionState<{ id: string }> = {
    success: true,
    error: "",
    data: { id: request.id },
  };

  // Create a wrapper function to pass the request ID to the server action
  const updateStatus = async (_prevState: any, formData: FormData) => {
    // Extract the status from the form data
    const status = formData.get("status") as RequestStatus;

    // Call the updateRequestStatus function with the correct parameters
    const result = await updateRequestStatus(request.id, status);

    if (result.success) {
      // Close the dialog and refresh the page
      setDialogOpen(false);
      router.refresh();
    }
    return result;
  };

  const [state, formAction, pending] = useActionState(updateStatus, initialState);

  // Handle opening the dialog for a specific action
  const handleAction = (action: string) => {
    setActionType(action);
    setNotes("");
    setDialogOpen(true);
  };

  // Get the new status based on the action type
  const getNewStatus = (): RequestStatus => {
    switch (actionType) {
      case "complete":
        return "completed";
      case "waitlist":
        return "waitlist";
      case "processing":
        return "processing";
      case "close":
        return "closed";
      default:
        return request.status;
    }
  };

  // Get the dialog title based on the action type
  const getDialogTitle = (): string => {
    switch (actionType) {
      case "complete":
        return dictionary?.confirmComplete || "Confirm Completion";
      case "waitlist":
        return dictionary?.confirmWaitlist || "Confirm Waitlist";
      case "processing":
        return dictionary?.confirmProcessing || "Confirm Processing";
      case "close":
        return dictionary?.confirmClose || "Confirm Closing";
      default:
        return dictionary?.confirmAction || "Confirm Action";
    }
  };

  // Get the dialog description based on the action type
  const getDialogDescription = (): string => {
    switch (actionType) {
      case "complete":
        return (
          dictionary?.completeDescription ||
          "Are you sure you want to mark this request as completed? This action cannot be undone."
        );
      case "waitlist":
        return (
          dictionary?.waitlistDescription ||
          "Are you sure you want to place this request on the waitlist?"
        );
      case "processing":
        return (
          dictionary?.processingDescription ||
          "Are you sure you want to mark this request as processing?"
        );
      case "close":
        return dictionary?.closeDescription || "Are you sure you want to close this request?";
      default:
        return dictionary?.actionDescription || "Are you sure you want to perform this action?";
    }
  };

  // Determine which buttons to show based on the current status and permissions
  const renderButtons = () => {
    const buttons = [];

    // Draft status actions
    if (request.status === "draft") {
      buttons.push(
        <Button
          key="processing"
          onClick={() => handleAction("processing")}
          disabled={pending}
          className="gap-2"
        >
          <ClipboardList className="h-4 w-4" />
          {dictionary?.actions?.processing || "Start Processing"}
        </Button>
      );
    }

    // Processing status actions
    if (request.status === "processing") {
      if (permissions.canComplete) {
        buttons.push(
          <Button
            key="complete"
            onClick={() => handleAction("complete")}
            disabled={pending}
            className="gap-2"
          >
            <CheckCircle className="h-4 w-4" />
            {dictionary?.actions?.complete || "Mark as Completed"}
          </Button>
        );
      }

      if (permissions.canWaitlist) {
        buttons.push(
          <Button
            key="waitlist"
            onClick={() => handleAction("waitlist")}
            variant="secondary"
            disabled={pending}
            className="gap-2"
          >
            <Clock className="h-4 w-4" />
            {dictionary?.actions?.waitlist || "Add to Waitlist"}
          </Button>
        );
      }
    }

    // Waitlist status actions
    if (request.status === "waitlist") {
      buttons.push(
        <Button
          key="processing"
          onClick={() => handleAction("processing")}
          disabled={pending}
          className="gap-2"
        >
          <ClipboardList className="h-4 w-4" />
          {dictionary?.actions?.processing || "Start Processing"}
        </Button>
      );
    }

    // Only draft, processing, and waitlist requests can be closed
    if (
      request.status === "draft" ||
      request.status === "processing" ||
      request.status === "waitlist"
    ) {
      buttons.push(
        <Button
          key="close"
          onClick={() => handleAction("close")}
          variant="destructive"
          disabled={pending}
          className="gap-2"
        >
          <XCircle className="h-4 w-4" />
          {dictionary?.actions?.close || "Close Request"}
        </Button>
      );
    }

    return buttons;
  };

  return (
    <>
      <div className="flex flex-wrap gap-2">
        {pending ? (
          <Button disabled className="gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            {dictionary?.processing || "Processing..."}
          </Button>
        ) : (
          renderButtons()
        )}
      </div>

      <AlertDialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <AlertDialogContent>
          <form action={formAction}>
            <AlertDialogHeader>
              <AlertDialogTitle>{getDialogTitle()}</AlertDialogTitle>
              <AlertDialogDescription>{getDialogDescription()}</AlertDialogDescription>
            </AlertDialogHeader>

            {/* Display error if there is one */}
            {!state.success && state.error && (
              <Alert variant="destructive" className="my-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{state.error}</AlertDescription>
              </Alert>
            )}

            {/* Hidden input for the new status */}
            <input type="hidden" name="status" value={getNewStatus()} />
            <input type="hidden" name="lang" value={lang} />

            {/* Notes field for close action or other actions that require notes */}
            {actionType === "close" && (
              <div className="my-4">
                <Label htmlFor="notes" className="mb-2 block">
                  {dictionary?.closeReason || "Reason for Closing"}
                </Label>
                <Textarea
                  id="notes"
                  name="close_reason"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder={
                    dictionary?.closeReasonPlaceholder || "Enter reason for closing the request"
                  }
                  required
                  rows={3}
                />
              </div>
            )}

            <AlertDialogFooter className="mt-4">
              <AlertDialogCancel disabled={pending}>
                {dictionary?.cancel || "Cancel"}
              </AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button type="submit" disabled={pending}>
                  {pending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {dictionary?.processing || "Processing..."}
                    </>
                  ) : (
                    dictionary?.confirm || "Confirm"
                  )}
                </Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </form>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
