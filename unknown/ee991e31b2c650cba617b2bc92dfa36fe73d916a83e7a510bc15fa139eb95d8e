import { WizardConfig } from "../types";
import { employeeWizardConfig } from "../../(features)/employee-wizard/config/employeeWizardConfigData";
import { requestWizardConfig } from "../../(features)/request-wizard/config/requestWizardConfigData";
import { logger } from "@/lib/logger/services/LoggerService";

/**
 * Registry of wizard configurations
 * This will be populated by specific wizard implementations
 */
const wizardConfigs: Record<string, WizardConfig> = {
  [employeeWizardConfig.workflowType]: employeeWizardConfig,
  [requestWizardConfig.workflowType]: requestWizardConfig,
};

/**
 * Register a wizard configuration
 * @param config The wizard configuration to register
 */
export function registerWizard(config: WizardConfig): void {
  logger.info(`Registering wizard: ${config.workflowType}`);
  wizardConfigs[config.workflowType] = config;
}

/**
 * Get a wizard configuration by workflow type
 * @param workflowType The workflow type
 * @returns The wizard configuration
 * @throws Error if the wizard configuration is not found
 */
export function getWizardConfig(workflowType: string): WizardConfig {
  const config = wizardConfigs[workflowType];

  if (!config) {
    throw new Error(`Wizard configuration not found for workflow type: ${workflowType}`);
  }

  return config;
}

/**
 * Get all registered wizard configurations
 * @returns All wizard configurations
 */
export function getAllWizardConfigs(): Record<string, WizardConfig> {
  return { ...wizardConfigs };
}
