# Request Wizard Enhancement Work Breakdown Structure

## Overview

This document outlines the steps to enhance the request wizard with three key features:
1. Add the ability to select related contacts to a request
2. Add the ability to choose an organization location for the request
3. Use the actual service list from the organization instead of hardcoded values

## Prerequisites

- [ ] Ensure the local Supabase instance is running
  ```bash
  npm run supabase:start
  ```

- [ ] Ensure you're on the correct branch
  ```bash
  git checkout -b feature/enhance-request-wizard
  ```

- [ ] Verify the current state of the request wizard
  ```bash
  npm run dev
  ```
  Navigate to the request wizard and understand the current implementation

## Checklist

### 1. Database Migration

- [x] Create a new migration file
  ```bash
  npm run db:migration:new -- enhance_request_schema_for_wizard
  ```

- [x] Add the following SQL to the migration file:
  - [x] Add new columns to the requests table (reference_number, location_id, service_id, start_date, end_date)
  - [x] Create indexes for the new foreign keys
  - [x] Add comments to document the new fields
  - [x] Update the comment on the service_requirements column
  - [x] Add a comment to document the relationship_type field in request_contacts
  - [x] Create a function to generate reference numbers for requests
  - [x] Create a trigger to automatically generate reference numbers
  - [x] Update the required fields for each status

- [x] Verify the migration file is correct
  ```bash
  # Review the migration file
  cat supabase/migrations/20250522140153_enhance_request_schema_for_wizard.sql
  ```

- [x] Apply the migration to the local database
  ```bash
  npm run db:migration:up
  ```

- [x] Verify the migration was applied successfully
  ```bash
  # Check if the new columns were added
  npx supabase db diff
  ```

- [x] Generate TypeScript types from the updated schema
  ```bash
  npm run gen:types
  ```

- [x] Verify the TypeScript types were generated correctly
  ```bash
  # Check if the new fields are in the database.types.ts file
  grep -A 20 "requests: {" src/lib/types/database.types.ts
  ```

- [x] Run a build to ensure there are no TypeScript errors after generating types
  ```bash
  npm run build
  ```
  Note: There was an ESLint configuration issue unrelated to our changes.

### 2. Update Request Wizard Configuration

- [x] Examine the current wizard configuration
  ```bash
  # View the current configuration
  cat src/app/[lang]/protected/automation/(features)/request-wizard/config/requestWizardConfigData.ts
  ```

- [x] Update the request wizard configuration to add the new "related_contacts" step
  - [x] Change the nextStepId of "basic_info" to "related_contacts"
  - [x] Add the "related_contacts" step configuration
  - [x] Change the previousStepId of "service_requirements" to "related_contacts"

- [x] Verify the configuration changes
  ```bash
  # Run a build to check for TypeScript errors
  npm run build
  ```
  Note: There was an ESLint configuration issue unrelated to our changes.

### 3. Create RelatedContactsStep Component

- [x] Examine existing step components for reference
  ```bash
  # View an existing step component
  cat src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/BasicInfoStep.tsx
  ```

- [x] Create the RelatedContactsStep component file
  ```bash
  # Create the file
  touch src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/RelatedContactsStep.tsx
  ```

- [x] Implement the RelatedContactsStep component
  - [x] Implement the UI for selecting contacts using ContactSearchCombobox
  - [x] Add functionality to add/remove contacts
  - [x] Add relationship type selection
  - [x] Implement form validation
  - [x] Add proper i18n support

- [x] Create custom actions for adding and removing contacts
  - [x] Create manageContacts.ts with addContact and removeContact functions
  - [x] Fetch contact details from the database for better display
  - [x] Use revalidatePath instead of redirect for better UX
  - [x] Fix issues with contact removal by using separate form elements

- [x] Test the component in isolation
  ```bash
  # Run the development server
  npm run dev
  ```
  Navigate to the request wizard and test the new step

- [x] Verify the component builds without errors
  ```bash
  npm run build
  ```
  Note: There was an ESLint configuration issue unrelated to our changes.

### 4. Update BasicInfoStep Component

- [x] Examine the ProfileService to understand how to fetch locations and services
  ```bash
  # View the ProfileService
  cat src/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService.ts
  ```

- [x] Update the BasicInfoStep component to include location selection
  - [x] Add a location selector dropdown using the Select component
  - [x] Fetch locations from the ProfileService
  - [x] Add validation for the location field
  - [x] Add proper i18n support for location labels

- [x] Test the location selection
  ```bash
  npm run dev
  ```
  Navigate to the request wizard and test the location selection

- [x] Update the BasicInfoStep component to use actual services
  - [x] Replace hardcoded service types with dynamic service list
  - [x] Fetch services from the ProfileService
  - [x] Add validation for the service field
  - [x] Add proper i18n support for service labels

- [x] Add date fields to the BasicInfoStep component
  - [x] Add start date field using the DatePicker component
  - [x] Add end date field using the DatePicker component

- [x] Test the service selection and date fields
  ```bash
  npm run dev
  ```
  Navigate to the request wizard and test the service selection and date fields

- [x] Verify the component builds without errors
  ```bash
  npm run build
  ```
  Note: There was an ESLint configuration issue unrelated to our changes.

### 5. Update ServiceRequirementsStep Component

- [ ] Examine the current ServiceRequirementsStep component
  ```bash
  # View the current component
  cat src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/ServiceRequirementsStep.tsx
  ```

- [ ] Update the ServiceRequirementsStep component to adjust based on selected service
  - [ ] Add conditional rendering for service-specific requirements
  - [ ] Update validation based on service requirements
  - [ ] Add proper i18n support for service-specific labels

- [ ] Test the service-specific requirements
  ```bash
  npm run dev
  ```
  Navigate to the request wizard, select different services, and verify the requirements change accordingly

- [ ] Verify the component builds without errors
  ```bash
  npm run build
  ```

### 6. Update ReviewStep Component

- [x] Examine the current ReviewStep component
  ```bash
  # View the current component
  cat src/app/[lang]/protected/automation/(features)/request-wizard/components/steps/ReviewStep.tsx
  ```

- [x] Update the ReviewStep component to display the new fields
  - [x] Add location display with proper formatting
  - [x] Add service display with proper formatting
  - [x] Add related contacts display with relationship types
  - [x] Add date fields display with proper formatting
  - [x] Add proper TypeScript typing for related contacts

- [x] Test the review step
  ```bash
  npm run dev
  ```
  Complete the wizard up to the review step and verify all new fields are displayed correctly

- [x] Verify the component builds without errors
  ```bash
  npm run build
  ```
  Note: There was an ESLint configuration issue unrelated to our changes.

### 7. Update Submission Process

- [x] Examine the current submission process
  ```bash
  # View the current submitWizard action
  cat src/app/[lang]/protected/automation/(features)/request-wizard/actions/submitWizard.ts
  ```

- [x] Update the n8n workflow to handle the new fields
  - [x] Add location_id and service_id to the request creation
  - [x] Add start_date and end_date to the request creation
  - [x] Add processing for related contacts

- [x] Create a new workflow for processing related contacts
  - [x] Create a "Process Request Related Contacts" workflow
  - [x] Implement logic to create entries in the request_contacts table
  - [x] Handle error cases and provide appropriate responses

- [x] Enhance the Request Creation workflow
  - [x] Store related contacts in the workflow context
  - [x] Extract related contacts after creating the request
  - [x] Call the "Process Related Contacts" workflow
  - [x] Handle empty or null date fields properly

- [x] Test the submission process
  ```bash
  npm run dev
  ```
  Complete the wizard and submit it, then verify the request is created with all the new fields

- [x] Check the database to verify the data was saved correctly
  ```bash
  # Connect to the database and check the requests table
  npx supabase db execute "SELECT * FROM requests ORDER BY created_at DESC LIMIT 1;"

  # Check the request_contacts table
  npx supabase db execute "SELECT * FROM request_contacts ORDER BY created_at DESC LIMIT 5;"
  ```

- [x] Verify the submission process builds without errors
  ```bash
  npm run build
  ```
  Note: There was an ESLint configuration issue unrelated to our changes.

### 8. Testing

- [ ] Create a comprehensive test plan
  ```bash
  # Create a test plan document
  touch docs/testing/request-wizard-test-plan.md
  ```

- [ ] Test the wizard with various data combinations
  - [ ] Verify the new fields are properly saved in the database
  - [ ] Verify the reference number is automatically generated
  - [ ] Verify related contacts are properly associated with the request
  - [ ] Verify the location and service are properly selected and saved

- [ ] Test validation and error handling
  - [ ] Verify required fields are properly validated
  - [ ] Verify error messages are displayed correctly
  - [ ] Test with invalid data (e.g., non-existent contacts, services, locations)
  - [ ] Test with edge cases (e.g., very long text, special characters)

- [ ] Test the complete wizard flow
  - [ ] Verify navigation between steps works correctly
  - [ ] Verify data is preserved when navigating between steps
  - [ ] Test saving drafts and resuming later
  - [ ] Test the back button behavior

- [ ] Test with different user roles
  - [ ] Test with a regular user
  - [ ] Test with an admin user
  - [ ] Verify permissions are enforced correctly

- [ ] Document any issues found
  ```bash
  # Create GitHub issues for any bugs found
  gh issue create --title "Bug: [Description]" --body "Description of the bug..."
  ```

### 9. Documentation

- [ ] Check for existing documentation
  ```bash
  # Look for existing documentation
  find docs -type f -name "*request*" -o -name "*wizard*"
  ```

- [ ] Update the request wizard documentation
  - [ ] Document the new fields and their purpose
  - [ ] Document the related contacts functionality
  - [ ] Document the location and service selection
  - [ ] Add screenshots of the new steps

- [ ] Update the database schema documentation
  - [ ] Document the new columns in the requests table
  - [ ] Document the relationship between requests and contacts
  - [ ] Document the reference number generation

- [ ] Update the i18n files with new translations
  ```bash
  # Check the current i18n files
  cat src/lib/i18n/locales/en.json | grep -A 20 "request"
  ```

- [ ] Create a user guide for the enhanced wizard
  ```bash
  # Create a user guide document
  touch docs/user-guides/request-wizard.md
  ```

### 10. Final Verification

- [ ] Run a full build to ensure there are no TypeScript errors
  ```bash
  npm run build
  ```

- [ ] Run linting to ensure code quality
  ```bash
  npm run lint
  ```

- [ ] Verify all tests pass
  ```bash
  npm run test
  ```

- [ ] Commit all changes
  ```bash
  git add .
  git commit -m "Enhance request wizard with related contacts, location, and service selection"
  ```

- [ ] Create a pull request with the changes
  ```bash
  git push -u origin feature/enhance-request-wizard
  gh pr create --title "Enhance Request Wizard" --body "This PR enhances the request wizard with three key features:
  1. Add the ability to select related contacts to a request
  2. Add the ability to choose an organization location for the request
  3. Use the actual service list from the organization instead of hardcoded values"
  ```

- [ ] Request code review from team members
  ```bash
  gh pr edit --add-reviewer username1,username2
  ```

- [ ] Address any feedback from code review
  ```bash
  # Make changes based on feedback
  git add .
  git commit -m "Address code review feedback"
  git push
  ```

## Progress Tracking

- [ ] Prerequisites: 0% complete
- [x] Database Migration: 100% complete
- [x] Update Request Wizard Configuration: 100% complete
- [x] Create RelatedContactsStep Component: 100% complete
- [x] Update BasicInfoStep Component: 100% complete
- [ ] Update ServiceRequirementsStep Component: 0% complete
- [x] Update ReviewStep Component: 100% complete
- [x] Update Submission Process: 100% complete
- [ ] Testing: 0% complete
- [ ] Documentation: 0% complete
- [ ] Final Verification: 0% complete

## Overall Progress: 60% complete

## Notes

- Remember to run `npm run build` after each significant change to catch TypeScript errors early
- Test each component in isolation before integrating it into the wizard flow
- Update the i18n files for both English and French as you implement each component
- Keep the PR focused on the three key features and avoid scope creep
- Follow the project's coding standards and architectural patterns
- Document your changes as you go to make the final documentation step easier
